import Sound from 'react-native-sound';

export interface AudioPlayerState {
  isPlaying: boolean;
  isLoading: boolean;
  error: string | null;
}

export class AudioClass {
  private sound: Sound | null = null;
  private stateCallback: (state: AudioPlayerState) => void;
  private currentState: AudioPlayerState = {
    isPlaying: false,
    isLoading: false,
    error: null,
  };

  constructor(stateCallback: (state: AudioPlayerState) => void) {
    this.stateCallback = stateCallback;
    // Enable playback in silence mode (iOS)
    Sound.setCategory('Playback');
  }

  private updateState(newState: Partial<AudioPlayerState>) {
    this.currentState = { ...this.currentState, ...newState };
    this.stateCallback(this.currentState);
  }

  async setupAudio(audioUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.updateState({ isLoading: true, error: null });

      // Release previous audio
      if (this.sound) {
        this.sound.stop();
        this.sound.release();
        this.sound = null;
      }

      // Create new audio instance
      // For bundle files, use Sound.MAIN_BUNDLE as the second parameter
      this.sound = new Sound(audioUrl, Sound.MAIN_BUNDLE, (error) => {
        this.updateState({ isLoading: false });

        if (error) {
          console.error('Failed to load audio:', error);
          this.updateState({ error: error.message || 'Failed to load audio' });
          reject(error);
          return;
        }

        console.log('Audio loaded successfully');
        resolve();
      });
    });
  }

  async playAudio(loop: boolean = false): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.sound) {
        const error = 'No audio loaded';
        this.updateState({ error });
        reject(new Error(error));
        return;
      }

      // Don't play if already playing
      if (this.currentState.isPlaying) {
        resolve();
        return;
      }

      this.updateState({ isPlaying: true, error: null });

      // Set loop if requested
      if (loop) {
        this.sound.setNumberOfLoops(-1); // -1 means infinite loop
      }

      this.sound.play((success) => {
        if (!loop && success) {
          this.updateState({ isPlaying: false });
        }

        if (success) {
          console.log('Audio played successfully');
          resolve();
        } else {
          const error = 'Audio playback failed';
          this.updateState({ isPlaying: false, error });
          reject(new Error(error));
        }
      });
    });
  }

  stopAudio(): void {
    if (this.sound) {
      this.sound.stop(() => {
        this.updateState({ isPlaying: false });
        console.log('Audio stopped');
      });
    }
  }

  pauseAudio(): void {
    if (this.sound && this.currentState.isPlaying) {
      this.sound.pause(() => {
        this.updateState({ isPlaying: false });
        console.log('Audio paused');
      });
    }
  }

  resumeAudio(): void {
    if (this.sound && !this.currentState.isPlaying) {
      this.updateState({ isPlaying: true, error: null });
      this.sound.play((success) => {
        if (!success) {
          this.updateState({ isPlaying: false, error: 'Resume playback failed' });
        }
        // Note: For looped audio, we don't set isPlaying to false here
        // because the audio continues playing in a loop
      });
    }
  }

  setVolume(volume: number): void {
    if (this.sound) {
      this.sound.setVolume(Math.max(0, Math.min(1, volume)));
    }
  }

  getDuration(): number {
    return this.sound ? this.sound.getDuration() : 0;
  }

  async getCurrentTime(): Promise<number> {
    return new Promise((resolve) => {
      if (this.sound) {
        this.sound.getCurrentTime((seconds) => {
          resolve(seconds);
        });
      } else {
        resolve(0);
      }
    });
  }

  setCurrentTime(time: number): void {
    if (this.sound) {
      this.sound.setCurrentTime(time);
    }
  }

  dispose(): void {
    if (this.sound) {
      this.sound.stop();
      this.sound.release();
      this.sound = null;
    }
    this.updateState({ isPlaying: false, isLoading: false, error: null });
  }
}
