export class StatusOrder {
  static success = 3;
  static proccess = 2;
  static cancel = 4;
}
export class CustomerType {
  static student = 2;
  static teacher = 1;
}
export class CustomerRankType {
  static normal = 2;
  static vip = 1;
}

export class StorageContanst {
  static CustomerId = 'CustomerId';
  static RecentCourse = 'recent_course';
  static accessToken = 'accessToken';
}
export class ExamType {
  static Try = 3;
  static Real = 2;
  static quiz = 1;
}
export class TestType {
  static Try = 1;
  static Real = 2;
}
export class StatusExam {
  static passed = 2;
  static process = 1;
  static fail = 3;
}
export class groupRole {
  static admin = 1;
  static subadmin = 2;
  static member = 3;
}
export class groupMemberRoleStatus {
  static invited = 0;
  static joined = 2;
}
export class FollowStatus {
  static Accept = 1;
  static Reject = 2;
  static Pending = 3;
  static None = 0;
}
export class GameStatus {
  static Completed = 1;
  static Pending = 2;
  static Locked = 3;
}
export class Sakupi {
  static game = 100;
  static login = 10;
  static buyCourse = 100;
  static quiz = 10;
  static exam = 50;
  static tryTest = 100;
  static createPost = 10;
  static flashcard = 200;
}
export class SakupiType {
  static buyCourse = 1;
  static login = 2;
  static createPost = 3;
  static quiz = 4;
  static exam = 5;
  static tryTest = 6;
  static game = 7;
  static flashcard = 8;
}

export class ReportType {
  static product = 1;
  static post = 2;
  static user = 3;
}
