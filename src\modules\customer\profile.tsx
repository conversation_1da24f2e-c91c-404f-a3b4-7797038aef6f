import {Image, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {useTranslation} from 'react-i18next';
import TitleWithBottom from '../../Screen/Layout/titleWithBottom';
import DefaultList from '../Default/listview/default';
import {
  ComponentStatus,
  FDialog,
  ListTile,
  showDialog,
  showSnackbar,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {navigate, navigateReset, RootScreen} from '../../router/router';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import {useDispatch} from 'react-redux';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import ConfigAPI from '../../Config/ConfigAPI';
import {BaseDA} from '../../base/BaseDA';
import ImagePicker from 'react-native-image-crop-picker';
import {CustomerRankType} from '../../Config/Contanst';
import {useEffect, useRef, useState} from 'react';
import {DrawerActions, useNavigation, useRoute} from '@react-navigation/native';
import {Ultis} from '../../utils/Utils';
import {LogoImg} from '../../Screen/Page/Home';
import FastImage from '@d11/react-native-fast-image';
import TitleWithImage from '../../Screen/Layout/titleWithImage';

const getActionList = (t: any) => [
  {
    id: 0,
    name: t('profile.account'),
    icon: 'fill/users/profile',
    background: ColorThemes.light.Secondary_5_Color_Background,
    colorIcon: ColorThemes.light.Secondary_5_Color_Main,
    route: RootScreen.SettingProfile,
  },

  {
    id: 3,
    name: t('profile.touchFaceId'),
    icon: 'fill/technology/face-recognition',
    background: ColorThemes.light.Info_Color_Background,
    colorIcon: ColorThemes.light.Info_Color_Main,
    route: RootScreen.BiometricSetting,
  },
  // {
  //   id: 4,
  //   name: 'Khoá học của tôi',
  //   icon: 'outline/shopping/list',
  //   action: 'mycourse',
  //   route: RootScreen.navigateESchoolView,
  // },
  {
    id: 5,
    name: t('profile.purchaseHistory'),
    icon: 'fill/shopping/shopping-cart',
    background: ColorThemes.light.Secondary_4_Color_Background,
    colorIcon: ColorThemes.light.Secondary_4_Color_Main,
    route: RootScreen.PurchaseHistory,
  },
  {
    id: 8,
    name: t('profile.savedVideos'),
    icon: 'fill/shopping/list',
    background: ColorThemes.light.Info_Color_Background,
    colorIcon: ColorThemes.light.Info_Color_Main,
    action: 'savedVideos',
    show: true,
  },
  {
    id: 7,
    name: t('profile.faq'),
    show: true,
    icon: 'fill/layout/circle-question',
    background: ColorThemes.light.Warning_Color_Background,
    colorIcon: ColorThemes.light.Warning_Color_Main,
    route: RootScreen.FAQView,
  },
  {
    id: 1,
    name: t('profile.policy'),
    show: true,
    icon: 'fill/shopping/list',
    background: ColorThemes.light.Success_Color_Background,
    colorIcon: ColorThemes.light.Success_Color_Main,
    route: RootScreen.PolicyView,
  },
  {
    id: 6,
    name: t('profile.logout'),
    show: true,
    action: 'logout',
    icon: 'outline/arrows/logout',
    background: ColorThemes.light.Error_Color_Background,
    colorIcon: ColorThemes.light.Error_Color_Main,
    route: RootScreen.login,
  },
];

export default function Profile() {
  const {t} = useTranslation();
  const dispatch = useDispatch<any>();
  const customer = useSelectorCustomerState().data;

  const [avt, setAvt] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const route = useRoute<any>();
  const actionList = getActionList(t);
  const dialogRef = useRef<any>(null);

  useEffect(() => {
    debugger;

    if (customer) {
      setAvt(customer.AvatarUrl);
    }
  }, [customer]);

  const pickerImg = async () => {
    const img = await ImagePicker.openPicker({
      multiple: false,
      cropping: true,
      cropperCircleOverlay: true,
    });
    if (img) {
      const resImgs = await BaseDA.uploadFiles([
        {
          uri: img.path,
          type: img.mime,
          name: img.filename ?? t('profile.newFileImg'),
        },
      ]);
      if (resImgs) {
        setIsLoading(true);
        await dispatch(
          CustomerActions.edit({
            ...customer,
            RanksData: undefined,
            RankInfor: undefined,
            AvatarUrl: resImgs[0].Id,
          }),
        ).then(() => {
          setAvt(resImgs[0].Id);
          setIsLoading(false);
          showSnackbar({
            message: t('profile.updateAvatarSuccess'),
            status: ComponentStatus.SUCCSESS,
            bottom: 60,
          });
          dispatch(CustomerActions.getInfor());
        });
      }
    }
  };
  const navigation = useNavigation<any>();

  return (
    <TitleWithImage
      title={t('profile.title')}
      prefix={
        <TouchableOpacity
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
          style={{padding: 4}}>
          <LogoImg />
        </TouchableOpacity>
      }
      iconActionPress={() => {
        navigate(RootScreen.Notification);
      }}>
      <FDialog ref={dialogRef} />
      <ScrollView
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
        }}>
        {customer ? (
          <View
            style={{
              margin: 16,
              padding: 24,
              gap: 16,
              borderRadius: 8,
              backgroundColor:
                ColorThemes.light.Neutral_Background_Color_Absolute,
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <TouchableOpacity
                style={{
                  width: 56,
                  height: 56,
                  borderRadius: 100,
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Main,
                }}
                onPress={customer ? pickerImg : undefined}>
                {isLoading ? (
                  <Image
                    style={{width: '100%', height: '100%', borderRadius: 100}}
                  />
                ) : customer?.AvatarUrl ? (
                  <FastImage
                    key={avt}
                    source={{
                      uri: ConfigAPI.getValidLink(avt),
                    }}
                    style={{width: '100%', height: '100%', borderRadius: 100}}
                  />
                ) : (
                  <View
                    style={{
                      width: 56,
                      height: 56,
                      borderRadius: 50,
                      backgroundColor: ColorThemes.light.Primary_Color_Main,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.heading7,
                        color:
                          ColorThemes.light.Neutral_Background_Color_Absolute,
                      }}>
                      {customer?.Name
                        ? customer.Name.charAt(0).toUpperCase()
                        : customer?.Email
                        ? customer.Email.charAt(0).toUpperCase()
                        : ''}
                    </Text>
                  </View>
                )}
                <View
                  style={{
                    position: 'absolute',
                    padding: 5,
                    borderRadius: 24,
                    backgroundColor: '#fff',
                    right: -2,
                    bottom: -2,
                  }}>
                  <Winicon
                    src="fill/entertainment/camera"
                    size={10}
                    color={'#000'}
                  />
                </View>
              </TouchableOpacity>
              {customer?.RankInfor ? (
                <View
                  style={{
                    width: 56,
                    height: 56,
                  }}>
                  {/* <Winicon
                    src={
                      ConfigAPI.getValidLink(customer?.RankInfor?.Icon)
                    }
                    size={56}
                  /> */}
                  <Image
                    source={{
                      uri: ConfigAPI.getValidLink(customer?.RankInfor?.Icon),
                    }}
                    style={{width: '100%', height: '100%'}}
                  />
                </View>
              ) : null}
            </View>
            <View style={{gap: 8}}>
              <View>
                <Text
                  style={{
                    ...TypoSkin.heading6,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                  }}>
                  {customer?.Name ?? customer?.Email ?? ''}
                </Text>
              </View>
              {customer?.Description ? (
                <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.Neutral_Text_Color_Body,
                  }}>
                  {customer?.Description ?? '-'}
                </Text>
              ) : null}
            </View>
          </View>
        ) : null}
        {/* scores */}
        {customer ? (
          <ListTile
            style={{
              padding: 0,
              marginBottom: 16,
            }}
            listtileStyle={{
              paddingRight: 16,
              paddingVertical: 13,
              gap: 8,
              borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
              borderBottomWidth: 1,
              marginLeft: 16,
            }}
            onPress={() => {
              navigate(RootScreen.ProfileRankScreen);
            }}
            leading={
              <View
                style={{
                  height: 32,
                  width: 32,
                  borderRadius: 4,
                  padding: 6,
                  backgroundColor: ColorThemes.light.Error_Color_Main,
                }}>
                <Winicon
                  src={'fill/user interface/star'}
                  color={ColorThemes.light.white}
                  size={20}
                />
              </View>
            }
            title={t('profile.yourPoints')}
            titleStyle={[
              TypoSkin.heading8,
              {color: ColorThemes.light.Neutral_Text_Color_Title},
            ]}
            subtitle={`${Ultis.money(customer?.Rank) ?? 0} ${t(
              'profile.points',
            )}`}
            subTitleStyle={[
              TypoSkin.heading8,
              {color: ColorThemes.light.Neutral_Text_Color_Title},
            ]}
            trailing={
              <Winicon
                src="outline/arrows/right-arrow"
                color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                size={16}
              />
            }
          />
        ) : null}
        {(!customer ? actionList.filter(item => item.show) : actionList).map(
          item => (
            <ListTile
              key={item.id}
              style={{
                padding: 0,
              }}
              listtileStyle={{
                paddingRight: 16,
                paddingVertical: 13,
                gap: 8,
                borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
                borderBottomWidth: 1,
                marginLeft: 16,
              }}
              onPress={() => {
                if (item.action === 'logout') {
                  // Handle logout action here
                  console.log('Logout action triggered');
                  // navigateReset(RootScreen.login);
                  if (!customer) {
                    dispatch(CustomerActions.logout());
                    return;
                  }
                  showDialog({
                    ref: dialogRef,
                    status: ComponentStatus.WARNING,
                    title: 'Bạn chắc chắn muốn đăng xuất?',
                    onSubmit: async () => {
                      dispatch(CustomerActions.logout());
                    },
                  });
                  return;
                }
                if (item.action === 'mycourse') {
                  navigation.navigate(RootScreen.navigateESchoolView, {
                    screen: 'Learn',
                  });
                  return;
                }
                if (item.action === 'savedVideos') {
                  navigation.navigate('SavedVideosScreen');
                  return;
                }
                if (item.route) navigate(item.route);
              }}
              leading={
                <View
                  style={{
                    height: 32,
                    width: 32,
                    borderRadius: 4,
                    padding: 6,
                    backgroundColor: item.colorIcon,
                  }}>
                  <Winicon
                    src={item.icon}
                    color={ColorThemes.light.white}
                    size={20}
                  />
                </View>
              }
              title={
                !customer && item.action === 'logout'
                  ? t('profile.login')
                  : item.name
              }
              titleStyle={[
                TypoSkin.heading8,
                {color: ColorThemes.light.Neutral_Text_Color_Title},
              ]}
              trailing={
                <Winicon
                  src="outline/arrows/right-arrow"
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                  size={16}
                />
              }
            />
          ),
        )}
      </ScrollView>
    </TitleWithImage>
  );
}
