import React, {useRef} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Animated} from 'react-native';
import {
  AppButton,
  ComponentStatus,
  ListTile,
  showDialog,
  showSnackbar,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import ScreenHeader from '../../../../Screen/Layout/header';
import {navigateBack} from '../../../../router/router';
import {onShare} from '../../../../features/share';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {CustomerRankType} from '../../../../Config/Contanst';
import store from '../../../../redux/store/store';
import {newsFeedActions} from '../../reducers/newsFeedReducer';

interface ProfileHeaderProps {
  profile: any;
  currentUser: any;
  avt: any;
  forEschool?: boolean;
  isHeaderVisible: boolean;
  newHeaderOpacity: Animated.AnimatedInterpolation<string | number>;
  onEditCover: () => void;
}

export const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  profile,
  currentUser,
  avt,
  forEschool,
  isHeaderVisible,
  newHeaderOpacity,
  onEditCover,
}) => {
  return (
    <>
      {/* Header mới - xuất hiện khi cuộn */}
      <Animated.View
        style={[
          styles.newHeader,
          {
            opacity: newHeaderOpacity,
          },
        ]}
        pointerEvents={isHeaderVisible ? 'auto' : 'none'}>
        <SafeAreaView style={styles.safeAreaTop} edges={['top']} />
        <ListTile
          isClickLeading
          style={styles.headerListTile}
          leading={
            <TouchableOpacity
              style={styles.headerBackButton}
              onPress={() => {
                navigateBack();
              }}>
              <Winicon
                src="outline/arrows/left-arrow"
                size={20}
                color={ColorThemes.light.Neutral_Text_Color_Title}
              />
            </TouchableOpacity>
          }
          title={profile?.Name}
          titleStyle={styles.headerTitle}
          subtitle={
            profile?.Rank
              ? `Hạng ${
                  profile?.Rank == CustomerRankType.normal ? 'Thường' : 'VIP'
                }`
              : ''
          }
          subTitleStyle={styles.headerSubtitle}
          trailing={
            <TouchableOpacity
              style={styles.headerShareButton}
              onPress={() => {
                onShare({
                  content: `${ConfigAPI.urlWeb}/profile-social?id=${profile?.Id}`,
                });
              }}>
              <Winicon
                src="fill/arrows/social-sharing"
                size={20}
                color={ColorThemes.light.Neutral_Text_Color_Title}
              />
            </TouchableOpacity>
          }
        />
      </Animated.View>
    </>
  );
};

export const ProfileOriginalHeader: React.FC<{
  profile: any;
  currentUser: any;
  avt: any;
  forEschool?: boolean;
  onEditCover: () => void;
  dialogRef: any;
}> = ({profile, currentUser, avt, dialogRef, forEschool, onEditCover}) => {
  const dispatch = store.dispatch;
  return (
    <Animated.View style={[styles.originalHeader]}>
      <ScreenHeader style={styles.screenHeader} />
      {/* image */}
      <View style={styles.coverImageContainer}>
        <AppButton
          prefixIcon={'outline/arrows/left-arrow'}
          prefixIconSize={20}
          backgroundColor={ColorThemes.light.Neutral_Background_Color_Absolute}
          textColor={ColorThemes.light.Neutral_Text_Color_Title}
          borderColor="transparent"
          containerStyle={styles.coverBackButton}
          onPress={navigateBack}
        />

        {profile?.Id === currentUser?.Id ? (
          <AppButton
            prefixIcon={'outline/entertainment/camera'}
            prefixIconSize={16}
            title={'Edit'}
            backgroundColor={
              ColorThemes.light.Neutral_Background_Color_Absolute
            }
            textColor={ColorThemes.light.Neutral_Text_Color_Title}
            borderColor="transparent"
            containerStyle={styles.editCoverButton}
            onPress={onEditCover}
          />
        ) : null}
        {profile?.AvatarUrl ? (
          <SkeletonImage
            key={profile?.AvatarUrl}
            source={{
              uri: profile?.AvatarUrl
                ? profile?.AvatarUrl.includes('http')
                  ? profile?.AvatarUrl
                  : ConfigAPI.getValidLink(profile?.AvatarUrl)
                : 'https://apieschool.itm.vn/api/file/img/ce5cc92f4b67415bb2622cf40d0693e8',
            }}
            height={248}
            style={styles.coverImage}
          />
        ) : (
          <View style={styles.coverFallback}>
            <Text style={styles.coverFallbackText}>
              {profile?.Name
                ? profile.Name.charAt(0).toUpperCase()
                : profile?.Email
                ? profile.Email.charAt(0).toUpperCase()
                : ''}
            </Text>
          </View>
        )}
      </View>
      <ListTile
        leading={
          profile?.AvatarUrl ? (
            <SkeletonImage
              key={profile?.AvatarUrl}
              source={{
                uri: profile?.AvatarUrl
                  ? profile?.AvatarUrl.includes('http')
                    ? profile?.AvatarUrl
                    : ConfigAPI.getValidLink(profile?.AvatarUrl)
                  : 'https://apieschool.itm.vn/api/file/img/ce5cc92f4b67415bb2622cf40d0693e8',
              }}
              height={56}
              width={56}
              style={styles.avatarImage}
            />
          ) : (
            <View style={styles.avatarFallback}>
              <Text style={styles.avatarFallbackText}>
                {profile?.Name
                  ? profile.Name.charAt(0).toUpperCase()
                  : profile?.Email
                  ? profile.Email.charAt(0).toUpperCase()
                  : ''}
              </Text>
            </View>
          )
        }
        title={profile?.Name ?? profile?.Email}
        titleStyle={styles.profileName}
        subtitle={
          profile?.Rank && !forEschool
            ? `Hạng ${
                profile?.Rank == CustomerRankType.normal ? 'Thường' : 'VIP'
              }`
            : ''
        }
        subTitleStyle={styles.profileRank}
        trailing={
          <View>
            {profile?.Id !== currentUser?.Id ? (
              <AppButton
                prefixIcon={'outline/user interface/lock'}
                prefixIconSize={16}
                title={'Block'}
                backgroundColor={ColorThemes.light.Error_Color_Main}
                textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
                borderColor="transparent"
                containerStyle={{
                  paddingHorizontal: 12,
                  borderRadius: 8,
                }}
                onPress={() => {
                  showDialog({
                    ref: dialogRef,
                    status: ComponentStatus.WARNING,
                    title: 'Bạn chắc chắn muốn chặn người này?',
                    content:
                      'Khi bị chặn, người này sẽ không thể gửi tin nhắn, xem nội dung, hoặc tương tác với bạn trong ứng dụng.',
                    onSubmit: async () => {
                      showSnackbar({
                        message: 'Bạn đã chặn người này thành công',
                        status: ComponentStatus.WARNING,
                      });
                      // tìm kiếm id tất cả bài viết của người này đang được hiển thị
                      const postIds = store
                        .getState()
                        .newsFeed.data.filter(
                          (post: any) => post.CustomerId === profile.Id,
                        )
                        ?.map((post: any) => post.Id);
                      // Ẩn tất cả bài đăng của người này
                      postIds?.forEach((postId: any) => {
                        dispatch(newsFeedActions.hidePostNocall(postId));
                      });

                      navigateBack();
                    },
                  });
                }}
              />
            ) : null}
          </View>
        }
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  newHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 3,
  },
  safeAreaTop: {
    height: 16,
  },
  headerListTile: {
    borderRadius: 0,
  },
  headerBackButton: {
    padding: 4,
    zIndex: 999,
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  headerSubtitle: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  headerShareButton: {
    padding: 4,
  },
  originalHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 320,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    zIndex: 1,
  },
  screenHeader: {
    backgroundColor: ColorThemes.light.transparent,
    position: 'absolute',
    height: 56,
    zIndex: 111,
  },
  coverImageContainer: {
    height: 248,
    width: '100%',
  },
  coverBackButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 111,
    paddingHorizontal: 12,
    borderRadius: 100,
    width: 40,
    height: 40,
  },
  editCoverButton: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    zIndex: 111,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  coverImage: {
    height: 248,
    width: '100%',
  },
  coverFallback: {
    height: 248,
    width: '100%',
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    alignItems: 'center',
    justifyContent: 'center',
  },
  coverFallbackText: {
    ...TypoSkin.heading1,
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  avatarImage: {
    height: 56,
    width: 56,
    borderRadius: 100,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  avatarFallback: {
    height: 56,
    width: 56,
    borderRadius: 100,
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarFallbackText: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  profileName: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  profileRank: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
});

export default styles;
