import { RootScreen } from '../router/router';

/**
 * Helper function để navigate giữa các stack khác nhau
 * @param navigation - Navigation object từ useNavigation hook
 * @param currentRoute - Route hiện tại từ useRoute hook
 * @param targetScreen - Screen muốn navigate tới
 * @param params - Parameters để truyền cho screen
 */
export const navigateToScreen = (
  navigation: any,
  currentRoute: any,
  targetScreen: string,
  params?: any
) => {
  // Danh sách screens thuộc Community stack
  const communityScreens = [
    RootScreen.PostDetail,
    RootScreen.GroupIndex,
    RootScreen.ProfileCommunity,
    RootScreen.AllGroupsLoadMore,
    RootScreen.createPost,
    RootScreen.NewsDetail,
  ];

  // Danh sách screens thuộc ESchool stack
  const eschoolScreens = [
    RootScreen.CourseDetail,
    RootScreen.LearnCourse,
    RootScreen.StartALTP,
    RootScreen.Test,
    RootScreen.SettingProfile,
    RootScreen.Notification,
    // ... thêm các screens kh<PERSON><PERSON> n<PERSON><PERSON> cần
  ];

  const isCommunityScreen = communityScreens.includes(targetScreen as RootScreen);
  const isEschoolScreen = eschoolScreens.includes(targetScreen as RootScreen);

  // Kiểm tra xem đang ở stack nào
  const isInCommunityStack = currentRoute.name === RootScreen.NotifCommunity || 
                            currentRoute.name === RootScreen.CommunityLayout ||
                            communityScreens.includes(currentRoute.name);

  const isInEschoolStack = currentRoute.name === RootScreen.Notification ||
                          currentRoute.name === RootScreen.navigateESchoolView ||
                          eschoolScreens.includes(currentRoute.name);

  if (isCommunityScreen) {
    if (isInCommunityStack) {
      // Đang ở Community stack, navigate trực tiếp
      navigation.push(targetScreen, params);
    } else {
      // Đang ở stack khác, cần chuyển sang Community stack
      navigation.navigate(RootScreen.navigateCommunityParent, {
        screen: RootScreen.navigateCommunityView,
        params: {
          screen: targetScreen,
          params: params,
        },
      });
    }
  } else if (isEschoolScreen) {
    if (isInEschoolStack) {
      // Đang ở ESchool stack, navigate trực tiếp
      navigation.push(targetScreen, params);
    } else {
      // Đang ở stack khác, cần chuyển sang ESchool stack
      navigation.navigate(RootScreen.navigateESchoolParent, {
        screen: RootScreen.navigateESchoolView,
        params: {
          screen: targetScreen,
          params: params,
        },
      });
    }
  } else {
    // Default navigation cho các screens khác
    navigation.navigate(targetScreen, params);
  }
};

/**
 * Helper function để navigate tới PostDetail từ bất kỳ đâu
 */
export const navigateToPostDetail = (
  navigation: any,
  currentRoute: any,
  postData: any
) => {
  navigateToScreen(navigation, currentRoute, RootScreen.PostDetail, {
    item: postData,
  });
};

/**
 * Helper function để navigate tới GroupIndex từ bất kỳ đâu
 */
export const navigateToGroupIndex = (
  navigation: any,
  currentRoute: any,
  groupId: string
) => {
  navigateToScreen(navigation, currentRoute, RootScreen.GroupIndex, {
    Id: groupId,
  });
};

/**
 * Helper function để navigate tới ProfileCommunity từ bất kỳ đâu
 */
export const navigateToProfileCommunity = (
  navigation: any,
  currentRoute: any,
  customerId: string
) => {
  navigateToScreen(navigation, currentRoute, RootScreen.ProfileCommunity, {
    Id: customerId,
  });
};
