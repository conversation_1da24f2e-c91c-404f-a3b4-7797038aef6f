import {Image, StyleSheet, View} from 'react-native';
import {Text} from 'react-native-paper';
import {TypoSkin} from '../../assets/skin/typography';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {
  ComponentStatus,
  FDialog,
  showDialog,
  Winicon,
} from 'wini-mobile-components';
import {navigateReset, RootScreen} from '../../router/router';
import Home, {ProfileEschoolView} from '../Page/Home';
import {ColorThemes} from '../../assets/skin/colors';
import {useRoute} from '@react-navigation/native';
import Profile from '../../modules/customer/profile';
import MyCourses from '../Page/myCourses';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {useRef} from 'react';
import GameListScreen from '../../modules/game/GameListScreen';
import TryingTests from '../../modules/exam/views/tryingTest/tryingTests';
import FastImage from '@d11/react-native-fast-image';
// import {useTranslation} from 'react-i18next';

const Tab = createBottomTabNavigator();

const bottomNavigateData = [
  {
    id: 0,
    name: 'Khoá học',
    component: Home,
    activeIcon: 'fill/business/presentation',
    inActiveIcon: 'outline/business/presentation',
  },
  {
    id: 1,
    name: 'Học tập',
    component: MyCourses,
    activeIcon: 'fill/business/books',
    inActiveIcon: 'outline/business/books',
  },
  {
    id: 2,
    name: 'Thi cử ',
    component: TryingTests,
    activeIcon: 'fill/user interface/f-chat',
    inActiveIcon: 'outline/user interface/f-chat',
  },
  {
    id: 3,
    name: 'Trò chơi',
    component: GameListScreen,
    activeIcon: 'fill/entertainment/board-game',
    inActiveIcon: 'outline/entertainment/board-game',
  },
  {
    id: 4,
    name: 'Cá nhân',
    component: Profile,
    activeIcon: 'fill/users/profile',
    inActiveIcon: 'outline/users/profile',
  },
];

export const dialogCheckAcc = (ref: any, router?: string) => {
  showDialog({
    ref: ref,
    status: ComponentStatus.WARNING,
    title: 'Vui lòng đăng nhập để sử dụng!',
    onSubmit: async () => {
      navigateReset(RootScreen.login);
    },
  });
};

export default function EschoolLayout() {
  const route = useRoute<any>();
  const dialogCheckAccRef = useRef<any>(null);
  const user = useSelectorCustomerState().data;

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FDialog ref={dialogCheckAccRef} />
      <Tab.Navigator
        initialRouteName={route.params?.rootName ?? undefined}
        screenOptions={{
          tabBarStyle: styles.bar,
          tabBarActiveTintColor: ColorThemes.light.Primary_Color_Main,
          tabBarInactiveTintColor:
            ColorThemes.light.Neutral_Text_Color_Subtitle,
          headerShown: false,
        }}>
        {bottomNavigateData.map((item, index) => {
          return (
            <Tab.Screen
              listeners={{
                tabPress: (e: any) => {
                  if (!user && item.id != 0 && item.id != 4) {
                    dialogCheckAcc(dialogCheckAccRef, undefined);
                    // Prevent default action
                    e.preventDefault();
                    return;
                  }
                  //Any custom code here
                },
              }}
              key={`${index}`}
              name={item.name}
              options={() => {
                return {
                  tabBarLabel: ({color, focused}: any) => (
                    <Text
                      style={[
                        TypoSkin.buttonText6,
                        {color: color, fontWeight: focused ? 'bold' : '400'},
                      ]}>
                      {item.name}
                    </Text>
                  ),
                  tabBarIcon: ({color, focused}: any) =>
                    item.id === 4 ? (
                      !user?.Id ? (
                        <FastImage
                          source={require('../../assets/appstore.png')}
                          style={{width: 30, height: 30}}
                        />
                      ) : (
                        <ProfileEschoolView onlyView={true} size={30} />
                      )
                    ) : (
                      <Winicon
                        src={focused ? item.activeIcon : item.inActiveIcon}
                        size={20}
                        color={color}
                      />
                    ),
                };
              }}
              component={item.component}
            />
          );
        })}
      </Tab.Navigator>
    </View>
  );
}

const styles = StyleSheet.create({
  bar: {
    backgroundColor: '#ffffff',
    borderStyle: 'solid',
    borderTopColor: '#F5F5F5',
  },
  tabBarButton: {
    // width: 64,
    height: '100%',
    // gap: 4,
    transform: [{translateY: -14}],
    alignItems: 'center',
  },
});
