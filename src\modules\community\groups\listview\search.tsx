import {
  Dimensions,
  FlatList,
  Pressable,
  RefreshControl,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  AppButton,
  Checkbox,
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  SkeletonImage,
  TextField,
  Winicon,
} from 'wini-mobile-components';
import React, {forwardRef, useEffect, useRef, useState} from 'react';
import {useForm} from 'react-hook-form';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {navigate, RootScreen} from '../../../../router/router';
import WScreenFooter from '../../../../Screen/Layout/footer';
import ScreenHeader from '../../../../Screen/Layout/header';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '../../../../redux/store/store';
import {followingGroupsActions} from '../../reducers/followingGroupsReducer';
import {GroupActions} from '../../reducers/groupReducer';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {GroupCardShimmer} from './groups';
import FastImage from '@d11/react-native-fast-image';

export const SearchGroupIndex = forwardRef(function SearchIndex(
  data: any,
  ref: any,
) {
  const [searchValue, setSearchValue] = useState('');
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const filterMethods = useForm({shouldFocusError: false});

  const [isRefresh, setRefresh] = useState(false);

  const {t} = useTranslation();
  const navigation = useNavigation<any>();

  const onRefresh = async () => {
    setRefresh(true);
    setSearchValue('');
    loadGroups();
    setRefresh(false);
  };
  const dispatch: AppDispatch = useDispatch();
  const {groups, isLoading, error, hasMore, page} = useSelector(
    (state: RootState) => state.group,
  );
  const followingGroups = useSelector(
    (state: RootState) => state.followingGroups.groups,
  );
  const creatorGroups = useSelector(
    (state: RootState) => state.myGroups.groups,
  );

  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    loadGroups();
  }, [activeTab]);

  const loadGroups = (refresh = true) => {
    if (refresh) {
      dispatch(GroupActions.getAllGroups(1, 10));
    } else if (hasMore) {
      dispatch(GroupActions.getAllGroups(page + 1, 10));
    }
  };

  const handleJoinGroup = async (group: any) => {
    await dispatch(followingGroupsActions.followGroup(group));
  };

  const isFollowing = (groupId: string) => {
    return (
      followingGroups.some(group => group.Id === groupId) ||
      creatorGroups.some(group => group.Id === groupId)
    );
  };

  const renderItem = ({item}: any) => {
    const following = isFollowing(item.Id);

    return (
      <ListTile
        onPress={() => {
          hideBottomSheet(ref);
          navigate(RootScreen.GroupIndex, {Id: item.Id});
        }}
        style={{
          borderColor: ColorThemes.light.Neutral_Border_Color_Main,
          borderWidth: 1,
          marginBottom: 16,
        }}
        title={item.Name}
        titleStyle={{
          ...TypoSkin.heading7,
          color: ColorThemes.light.Neutral_Text_Color_Title,
        }}
        subtitle={
          <Text
            style={{
              ...TypoSkin.subtitle3,
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
            }}>
            {item.MemberCount} members
          </Text>
        }
        leading={
          <FastImage
            source={
              item?.Thumb
                ? {
                    uri: item?.Thumb?.includes('https')
                      ? item?.Thumb
                      : ConfigAPI.getValidLink(item?.Thumb),
                  }
                : require('../../../../assets/appstore.png')
            }
            style={{
              height: 56,
              width: 56,
              borderRadius: 100,
              backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
            }}
          />
        }
        bottom={
          <View style={{width: '100%', flex: 1, paddingTop: 8, gap: 8}}>
            {item.Description && (
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.Neutral_Text_Color_Body,
                }}>
                {item.Description}
              </Text>
            )}
            {following ? null : (
              <AppButton
                title={following ? 'Đã tham gia' : 'Tham gia'}
                onPress={() => (following ? null : handleJoinGroup(item))}
                textStyle={{
                  ...TypoSkin.buttonText5,
                  color: following
                    ? ColorThemes.light.Neutral_Text_Color_Subtitle
                    : ColorThemes.light.Info_Color_Main,
                }}
                backgroundColor={ColorThemes.light.transparent}
                borderColor={
                  following
                    ? ColorThemes.light.Neutral_Text_Color_Subtitle
                    : ColorThemes.light.Info_Color_Main
                }
                containerStyle={{
                  borderRadius: 8,
                  paddingHorizontal: 8,
                  alignItems: 'center',
                  alignSelf: 'baseline',
                  height: 24,
                }}
              />
            )}
          </View>
        }
      />
    );
  };

  const renderEmptyComponent = () => {
    if (isLoading) {
      return (
        <View style={{gap: 16}}>
          <GroupCardShimmer />
          <GroupCardShimmer />
          <GroupCardShimmer />
        </View>
      );
    }
  };

  return (
    <Pressable
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 65,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          paddingVertical: 4,
        }}
        title={'Tìm kiếm'}
        prefix={<View />}
        action={
          <TouchableOpacity
            onPress={() => hideBottomSheet(ref)}
            style={{padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.Neutral_Text_Color_Body}
            />
          </TouchableOpacity>
        }
        bottom={
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              paddingHorizontal: 16,
              height: 56,
              gap: 8,
              paddingTop: 8,
              paddingBottom: 16,
            }}>
            <TextField
              style={{paddingHorizontal: 16, flex: 1, height: 40}}
              onChange={async (vl: string) => {
                setSearchValue(vl.trim());
              }}
              value={searchValue}
              placeholder="Tìm kiếm"
              prefix={
                <Winicon
                  src="outline/development/zoom"
                  size={14}
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                />
              }
            />
            {/* <AppButton
              onPress={() => {
                showBottomSheet({
                  ref: bottomSheetRef,
                  enableDismiss: true,
                  children: (
                    <PopupFilter
                      ref={bottomSheetRef}
                      filterMethods={filterMethods}
                      selectedAttributes={
                        filterMethods.watch('AttributeId') ?? []
                      }
                      onApply={value => {
                        filterMethods.setValue(
                          'AttributeId',
                          value.AttributeId,
                        );
                      }}
                    />
                  ),
                });
              }}
              backgroundColor={ColorThemes.light.transparent}
              borderColor={
                filterMethods.watch('AttributeId')?.length
                  ? ColorThemes.light.Primary_Color_Main
                  : ColorThemes.light.Neutral_Border_Color_Main
              }
              containerStyle={{
                height: 40,
                borderRadius: 8,
                paddingHorizontal: 8,
              }}
              prefixIconSize={18}
              prefixIcon={'outline/user interface/setup-preferences'}
              textColor={
                filterMethods.watch('AttributeId')?.length
                  ? ColorThemes.light.Primary_Color_Main
                  : ColorThemes.light.Neutral_Text_Color_Subtitle
              }
              textStyle={TypoSkin.subtitle3}
            /> */}
          </View>
        }
      />
      <FlatList
        data={
          searchValue
            ? groups.filter((item: any) =>
                item.Name.toLowerCase().includes(searchValue.toLowerCase()),
              )
            : groups
        }
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        renderItem={renderItem}
        keyExtractor={item => item.Id}
        contentContainerStyle={{
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        style={{width: '100%', height: '100%', paddingHorizontal: 16}}
        onRefresh={loadGroups}
        refreshing={isLoading}
        onEndReached={() => loadGroups(false)}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={renderEmptyComponent}
        ListFooterComponent={() => {
          return <View style={{height: 24}} />;
        }}
      />
    </Pressable>
  );
});

const PopupFilter = forwardRef(function PopupFilter(
  data: {
    filterMethods: any;
    onApply: (values: any) => void;
    selectedAttributes: [];
  },
  ref: any,
) {
  const {onApply, selectedAttributes} = data;
  const [selected, setSelected] = useState<Array<any>>([]);

  useEffect(() => {
    if (selectedAttributes.length) {
      setSelected(selectedAttributes);
    }
  }, [selectedAttributes.length, selectedAttributes]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 146,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          borderBottomColor: ColorThemes.light.Neutral_Background_Color_Main,
          borderBottomWidth: 0.5,
          shadowColor: 'rgba(0, 0, 0, 0.03)',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowRadius: 20,
          elevation: 20,
          shadowOpacity: 1,
        }}
        title={'Bộ lọc'}
        prefix={<View style={{width: 50}} />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => hideBottomSheet(ref)}
              size={20}
              color={ColorThemes.light.Neutral_Text_Color_Body}
            />
          </View>
        }
      />
      <View style={{flex: 1, paddingHorizontal: 16, paddingVertical: 16}}>
        <Text style={{...TypoSkin.label3, marginBottom: 8}}>Loại dịch vụ</Text>
        <FlatList
          data={[
            {Id: 1, Name: 'a'},
            {Id: 2, Name: 'b'},
          ]}
          scrollEnabled={false}
          renderItem={({item, index}) => (
            <TouchableOpacity
              key={index}
              onPress={() => {
                if (!selected.includes(item.Id)) {
                  setSelected([...selected, item.Id]);
                } else {
                  setSelected(selected.filter((id: any) => id !== item.Id));
                }
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 8,
                width: '50%',
              }}>
              <Checkbox
                value={selected.includes(item.Id)}
                onChange={v => {
                  if (v) {
                    setSelected([...selected, item.Id]);
                  } else {
                    setSelected(selected.filter((id: any) => id !== item.Id));
                  }
                }}
              />
              <Text
                style={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}>
                {item?.Name ?? '-'}
              </Text>
            </TouchableOpacity>
          )}
          //Setting the number of column
          numColumns={2}
          style={{width: '100%'}}
          contentContainerStyle={{gap: 16}}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
      <WScreenFooter
        style={{flexDirection: 'row', gap: 8, paddingHorizontal: 16}}>
        <AppButton
          title={'Làm mới'}
          backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            setSelected([]);
          }}
          textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
        />
        <AppButton
          title={'Áp dụng'}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            hideBottomSheet(ref);
            onApply({AttributeId: selected});
          }}
          textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});
