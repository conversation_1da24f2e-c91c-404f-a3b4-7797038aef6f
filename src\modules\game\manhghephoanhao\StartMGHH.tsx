import React, {useState, useRef, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
  Vibration,
  SafeAreaView,
  BackHandler,
} from 'react-native';
import GameOverModal from '../components/GameOverModel';
import {CardText} from '../components/CardText';
import {useSelector} from 'react-redux';
import {useMghhHook} from './redux/hook/MghhHook';
import {useGameHook} from '../../../redux/hook/gameHook';
import {RootState} from '../../../redux/store/store';
import {BottomGame} from '../components/BottomGame';
import {CardTitleGame} from '../components/CardTitleGame';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import ConfigAPI from '../../../Config/ConfigAPI';
import {
  checkPositionOrder,
  hasMaxSort,
  removeObjectByType,
  replaceObjectById,
} from '../utils/functions';
import {DropZone, Word} from './models/models';
import CountBadge from '../components/CountQuestions';
import ModelDescriptionQuestion from '../components/ModelDescriptionQuestion';
import ModelDoneLevel from '../components/ModelDoneLevel';
import {useFocusEffect, useRoute} from '@react-navigation/native';
import WinnerModal from './components/WinnerModal';
import {navigateBack} from '../../../router/router';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import ModelPauseGame from '../components/ModelPauseGame';
import {shuffleArray} from '../../../utils/arrayUtils';
import {useGameAudio} from '../ailatrieuphu/hooks/useGameAudio';

const StartMGHH = () => {
  const route = useRoute();
  const {milestoneId, competenceId} = route.params as {
    milestoneId: string;
    competenceId: string;
  };
  const {isGameOver, messageGameOver} = useSelector(
    (state: RootState) => state.gameStore,
  );
  const {
    listQuestions,
    currentQuestion,
    questionDone,
    totalQuestion,
    currentLevel,
    config,
    loadData,
    dataListQuestion,
  } = useSelector((state: RootState) => state.MGHHStore);
  const {
    audioState,
    playCorrectAnswer,
    playInCorrectAnswer,
    playWin,
    playGameOver,
    playTimeWarning,
    stopCurrentSound,
    stopTimeWarning,
    clearAllSounds,
    toggleMute,
  } = useGameAudio();
  const [showModelConfirm, setShowModelConfirm] = useState(false);
  const [showModelSupport, setShowModelSupport] = useState(false);
  const [answersState, setAnswersState] = useState<Word[]>([]);
  const [listDropZone, setListDropZone] = useState<DropZone[]>([]);
  const [textAnswer, setTextAnswer] = useState<string>('');
  const [isQuestionError, setIsQuestionError] = useState(false);
  const [isPauseGame, setIsPauseGame] = useState(false);
  const [isWinnerLevel, setIsWinnerLevel] = useState(false);
  const [isWinnerQuestion, setIsWinnerQuestion] = useState(false);
  const [isWinGame, setIsWinGame] = useState(false);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const gameHook = useGameHook();
  const mghhHook = useMghhHook();
  const refDropZone = useRef<{[key: string]: View | null}>({});

  // Kiểm tra xem câu hỏi hiện tại có hint không
  const hasHint = currentQuestion?.hint && currentQuestion.hint.trim() !== '';
  const shouldShowHintButton = Boolean(hasHint);
  // Handle screen focus/blur for audio cleanup
  useFocusEffect(
    useCallback(() => {
      console.log('StartDHBC focused');

      return () => {
        console.log('StartDHBC blurred - clearing all sounds');
        clearAllSounds();
      };
    }, [clearAllSounds]),
  );
  // Handle hardware back button
  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        console.log('DHBC: Hardware back button pressed');
        clearAllSounds();
        return false; // Let default behavior handle navigation
      },
    );
    return () => backHandler.remove();
  }, [clearAllSounds]);
  useEffect(() => {
    initData();
    return () => {
      mghhHook.restartGame();
      refDropZone.current = {};
    };
  }, [milestoneId, competenceId]);

  // Theo dõi khi dataListQuestion thay đổi (dữ liệu thô từ API)
  useEffect(() => {
    console.log('=== MGHH useEffect [dataListQuestion] ===');
    console.log('dataListQuestion length:', dataListQuestion?.length);
    console.log('milestoneId:', milestoneId);
    console.log('competenceId:', competenceId);

    // Khi có dữ liệu mới từ API, start game
    if (dataListQuestion && dataListQuestion.length > 0) {
      console.log('New data loaded, calling onStartGame...');
      onStartGame();
    }
  }, [dataListQuestion]);

  useEffect(() => {
    if (currentQuestion) {
      const listWords = shuffleArray(currentQuestion.listWords);
      setTextAnswer('');
      setAnswersState(listWords);
      setListDropZone(
        listWords.map((_, index) => ({
          id: `dropzone-${index}-${Math.random().toString(36).substring(2, 9)}`,
          word: null,
          x: 0,
          y: 0,
          width: 0,
          height: 0,
        })),
      );
    }
  }, [currentQuestion]);

  useEffect(() => {
    if (currentLevel > 0) {
      const config = getCurrentConfig();
      gameHook.setData({stateName: 'time', value: config?.timeLimit || 0});
    }
  }, [currentLevel]);

  // khởi tạo dữ liệu
  const initData = () => {
    console.log('=== MGHH initData ===');
    console.log('milestoneId:', milestoneId);
    console.log('competenceId:', competenceId);
    resetData();
    // Reset loadData để tránh race condition
    mghhHook.resetLoadData();
    console.log('Loading data for stage:', milestoneId);

    // Load dữ liệu
    mghhHook.loadInitData({
      gameId: ConfigAPI.gameMGHH,
      stage: milestoneId as unknown as number,
      competenceId: competenceId,
    });

    gameHook.getCurrentScore(ConfigAPI.gameMGHH);
  };

  // bắt đầu game
  const onStartGame = () => {
    console.log('=== MGHH onStartGame ===');
    console.log('dataListQuestion length:', listQuestions?.length);
    console.log('currentLevel will be:', 1);
    resetData();
    debugger;
    mghhHook.startGame();
    gameHook.restartGame();
  };

  // bắt đầu lại game
  const onRestartGame = () => {
    console.log('onRestartGame');
    mghhHook.restartGame();
    setTimeout(() => {
      onStartGame();
    }, 100);
  };

  // lấy config hiện tại
  const getCurrentConfig = () => {
    switch (currentLevel) {
      case 1:
        return config.configLv1;
      case 2:
        return config.configLv2;
      case 3:
        return config.configLv3;
      default:
        return {};
    }
  };

  // reset dữ liệu
  const resetData = () => {
    setIsWinnerLevel(false);
    setIsWinnerQuestion(false);
    setIsWinGame(false);
    setIsQuestionError(false);
    setAnswersState([]);
    setListDropZone([]);
    setTextAnswer('');
  };

  // lấy bonus
  const getBonus = () => {
    const config = getCurrentConfig();
    return config?.score || 0;
  };

  // sử dụng trợ giúp
  const onUseHint = () => {
    const config = getCurrentConfig();
    gameHook.updateScore({
      name: `MGHH_${milestoneId}`,
      totalScore: -(config?.scoreHint || 0),
      gameId: ConfigAPI.gameMGHH,
      competenceId: competenceId,
      currentMilestoneId: milestoneId as unknown as number,
      status: 0,
    });
    setShowModelSupport(true);
  };

  // game over
  const gameOver = (message: string) => {
    gameHook.gameOver(message);
    playGameOver();
  };

  // sang câu hỏi tiếp theo
  const nextQuestion = () => {
    gameHook.continueGame();
    setIsWinnerQuestion(false);
    mghhHook.setData({
      stateName: 'questionDone',
      value: questionDone + 1,
    });
    mghhHook.nextQuestion();
  };

  // thắng level
  const onWinnerLevel = async () => {
    gameHook.pauseGame();
    if (currentLevel === 3) {
      playWin();
      setIsWinGame(true);
      return;
    }
    setIsWinnerLevel(true);
  };

  // thắng câu hỏi
  const onWinnerQuestion = async () => {
    gameHook.pauseGame();
    setIsWinnerQuestion(true);
  };

  // sang level tiếp theo
  const onNextLevel = () => {
    setIsWinnerLevel(false);
    mghhHook.nextLevel();
    gameHook.continueGame();
  };

  // bắt đầu lại level
  const restartLevel = () => {
    setIsQuestionError(false);
    mghhHook.restartLevel();
  };

  // Kiểm tra đáp án
  const checkAnswer = () => {
    const words: Word[] = [];
    listDropZone.forEach(zone => {
      if (zone.word) {
        words.push(zone.word);
      }
    });
    if (words.length !== currentQuestion?.listWords.length) {
      playInCorrectAnswer();
      const textAnswer = words.map(word => word.text).join(' ');
      setTextAnswer(textAnswer);
      setIsQuestionError(true);
      mghhHook.setData({stateName: 'currentQuestion', value: null});
      return;
    };
    const isCorrect = checkPositionOrder(words);
    if (isCorrect) {
      console.log('listQuestions', listQuestions);
      console.log('currentQuestion', currentQuestion);
      playCorrectAnswer();
      if (hasMaxSort(listQuestions, currentQuestion)) return onWinnerLevel();
      onWinnerQuestion();
    } else {
      // Vibration.vibrate([0, 500, 200, 500]);
      playInCorrectAnswer();
      const textAnswer = words.map(word => word.text).join(' ');
      setTextAnswer(textAnswer);
      setIsQuestionError(true);
      mghhHook.setData({stateName: 'currentQuestion', value: null});
    }
  };

  // Hàm xử lý khi thả từ vào drop zone
  const handleDropToZone = (word: Word) => {
    const dropZone = listDropZone.find(zone => zone.word === null);
    if (!dropZone || dropZone.word) return;
    dropZone.word = word;
    const newListDropZone = replaceObjectById(dropZone, listDropZone);
    setListDropZone(newListDropZone);

    const newAnswersState = removeObjectByType(answersState, word, 'id');
    setAnswersState(newAnswersState);
  };

  // Hàm xử lý khi click vào từ trong drop zone
  const handleWordClick = (zoneId: string) => {
    const dropZone = listDropZone.find(zone => zone.id === zoneId);
    if (!dropZone || !dropZone.word) return;

    const cloneAnswersState = [...answersState];
    cloneAnswersState.push(dropZone.word);
    setAnswersState(cloneAnswersState);

    dropZone.word = null;
    const newListDropZone = replaceObjectById(dropZone, listDropZone);
    setListDropZone(newListDropZone);
  };

  // Component các từ để kéo thả
  const DraggableWord = ({word}: {word: Word}) => {
    return (
      <TouchableOpacity
        onPress={() => {
          handleDropToZone(word);
        }}>
        <CardText text={word.text} />
      </TouchableOpacity>
    );
  };

  // Component ô trong drop zone
  const SentenceWord = ({dropZone}: {dropZone: DropZone}) => {
    const isAnswer = dropZone.word;

    return (
      <TouchableOpacity
        ref={ref => {
          refDropZone.current[dropZone.id] = ref;
        }}
        onPress={() => (isAnswer ? handleWordClick(dropZone.id) : null)}
        style={[
          styles.sentenceWord,
          styles.dropZone,
          isAnswer && styles.filledDropZone,
        ]}>
        <Text
          style={[styles.sentenceText, !isAnswer && styles.placeholderText]}>
          {dropZone.word?.text || '...'}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={{flex: 1}}>
      <ImageBackground
        style={styles.backgroundImage}
        source={require('./assets/background.png')}
        resizeMode="cover">
        <View style={styles.mainContainer}>
          {/* Header */}
          <View style={styles.headerContainer}>
            <HeadGame
              timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
              isShowSuggest={shouldShowHintButton}
              onUseHint={() => setShowModelConfirm(true)}
              gameId={ConfigAPI.gameMGHH}
            />
            <LineProgressBar progress={(questionDone / totalQuestion) * 100} />
            <View style={styles.levelInfoRow}>
              <CardText text={`Cấp độ ${currentLevel}`} />

              <CountBadge
                current={questionDone + 1}
                total={totalQuestion}></CountBadge>
            </View>
          </View>

          <View style={styles.titleContainer}>
            <CardTitleGame
              showIcon={true}
              title={`${currentQuestion?.title}`}
              audioUrl={currentQuestion?.audioUrl || null}></CardTitleGame>
          </View>

          <View style={styles.gameContainer}>
            <View>
              <View style={styles.sentenceRow}>
                {React.useMemo(() => {
                  return listDropZone.map(zone => (
                    <SentenceWord key={zone.id} dropZone={zone} />
                  ));
                }, [listDropZone])}
              </View>
              <View style={styles.wordsContainer}>
                {React.useMemo(() => {
                  return answersState.map(word => (
                    <View key={word.id} style={styles.wordWrapper}>
                      <DraggableWord word={word} />
                    </View>
                  ));
                }, [answersState])}
              </View>
              <TouchableOpacity
                onPress={checkAnswer}
                style={styles.checkButton}
                activeOpacity={0.7}>
                <Text style={styles.checkButtonText}>Kiểm tra kết quả</Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.bottomContainer}>
            <BottomGame
              resetGame={onRestartGame}
              backGame={() => {
                navigateBack();
              }}
              pauseGame={() => setIsPauseGame(true)}
              volumeGame={() => {}}></BottomGame>
          </View>
        </View>
        <View style={styles.modalContainer}>
          <GameOverModal
            visible={isGameOver}
            onClose={() => {}}
            restartGame={onRestartGame}
            message={messageGameOver}
            isTimeOut={false}
            isShowHeader={true}
          />
          <ModelConfirm
            isShow={showModelConfirm}
            closeModal={() => setShowModelConfirm(false)}
            onConfirm={onUseHint}
            message={`Bạn sẽ bị trừ ${
              getCurrentConfig().scoreHint || 0
            } Sakupi khi sử dụng trợ giúp này`}
          />
          <HintModel
            isShow={showModelSupport}
            closeModal={() => setShowModelSupport(false)}
            text={currentQuestion?.hint || ''}
          />
          <GameOverModal
            visible={isQuestionError}
            onClose={() => {}}
            restartGame={restartLevel}
            message={'Tiếc quá sai rồi, làm lại nào'}
            isTimeOut={false}
            isShowCardText={true}
            statusCard={'error'}
            cardText={textAnswer}
          />
          <ModelDescriptionQuestion
            visible={isWinnerQuestion}
            onNext={nextQuestion}
            message={currentQuestion?.description || ''}
          />
          <ModelDoneLevel
            visible={isWinnerLevel}
            message={`Bạn đã vượt qua cấp ${currentLevel}`}
            onNextLevel={onNextLevel}
            gameId={ConfigAPI.gameMGHH}
            competenceId={competenceId}
            currentMilestoneId={milestoneId}
            gameConfig={getCurrentConfig()}
          />
          <WinnerModal
            visible={isWinGame}
            onClose={() => setIsWinGame(false)}
            restartGame={onRestartGame}
            competenceId={competenceId}
            currentMilestoneId={milestoneId}
            gameId={ConfigAPI.gameMGHH}
            totalScore={getBonus()}
          />
          <ModelPauseGame
            visible={isPauseGame}
            message={'Bạn đang tạm dừng trò chơi'}
            onContinue={() => setIsPauseGame(false)}
          />
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    marginHorizontal: 12,
    marginTop: 36,
    marginBottom: 20,
  },
  headerContainer: {},
  levelInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  titleContainer: {
    marginTop: 16,
  },
  gameContainer: {
    marginTop: 60,
  },
  wordWrapper: {
    margin: 4,
  },
  checkButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 20,
    alignSelf: 'center',
    elevation: 3,
  },
  checkButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
  },
  modalContainer: {
    zIndex: 1000,
  },
  sentenceRow: {
    paddingVertical: 20,
    borderRadius: 12,
    backgroundColor: 'white',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sentenceWord: {
    marginHorizontal: 4,
    marginVertical: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  dropZone: {
    backgroundColor: '#F8F8F8',
    borderWidth: 2,
    borderColor: '#DDD',
    borderStyle: 'dashed',
    minWidth: 60,
    minHeight: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filledDropZone: {
    backgroundColor: '#E8F5E8',
    borderColor: '#4CAF50',
    borderStyle: 'solid',
  },
  sentenceText: {
    fontSize: 18,
    color: '#333',
    fontWeight: '600',
    textAlign: 'center',
  },
  placeholderText: {
    color: '#999',
    fontSize: 16,
  },
  wordsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginTop: 20,
  },
});

export default StartMGHH;
