import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  Alert,
} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';

type VideoModalProps = {
  visible: boolean;
  onClose: () => void;
  onAddLink: (link: string) => void;
};

const VideoModal: React.FC<VideoModalProps> = ({
  visible,
  onClose,
  onAddLink,
}) => {
  const [videoLink, setVideoLink] = useState('');

  const validateVideoLink = (link: string): boolean => {
    // Kiểm tra các platform video phổ biến
    const videoRegex =
      /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be|vimeo\.com|dailymotion\.com|facebook\.com|instagram\.com|tiktok\.com|twitter\.com|x\.com)\/.+/;
    // Hoặc kiểm tra URL hợp lệ có chứa từ khóa video
    const urlRegex = /^https?:\/\/.+/;
    return videoRegex.test(link) || urlRegex.test(link);
  };

  const handleAddLink = () => {
    if (!videoLink.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập link video');
      return;
    }

    if (!validateVideoLink(videoLink.trim())) {
      Alert.alert('Lỗi', 'Link video không hợp lệ');
      return;
    }

    // chuyển đổi link nếu là link youtube thì sẽ chuyển về dạng embed
    if (
      videoLink.trim().includes('youtube.com') ||
      videoLink.includes('youtu.be')
    ) {
      const videoId = videoLink.trim().split('v=')[1];
      if (videoId) {
        onAddLink(`https://www.youtube.com/embed/${videoId}`);
      }
    } else {
      onAddLink(videoLink.trim());
    }

    setVideoLink('');
    onClose();
  };

  const handleClose = () => {
    setVideoLink('');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Gắn link video</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <Winicon
                src="outline/arrows/close"
                size={20}
                color={ColorThemes.light.Neutral_Text_Color_Subtitle}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Đường dẫn video:</Text>
            <TextInput
              style={styles.textInput}
              value={videoLink}
              onChangeText={setVideoLink}
              placeholder="Nhập đường dẫn video"
              placeholderTextColor={
                ColorThemes.light.Neutral_Text_Color_Subtitle
              }
              autoCapitalize="none"
              autoCorrect={false}
              keyboardType="url"
            />
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={handleClose}>
              <Text style={styles.cancelButtonText}>Hủy</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.addButton]}
              onPress={handleAddLink}>
              <Text style={styles.addButtonText}>Thêm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = {
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    padding: 20,
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: '100%' as const,
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 20,
  },
  modalTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  closeButton: {
    padding: 4,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    ...TypoSkin.subtitle2,
    color: ColorThemes.light.Neutral_Text_Color_Body,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: ColorThemes.light.Neutral_Text_Color_Body,
    backgroundColor: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row' as const,
    justifyContent: 'flex-end' as const,
    gap: 12,
  },
  button: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    minWidth: 80,
    alignItems: 'center' as const,
  },
  cancelButton: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Secondary,
  },
  addButton: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
  },
  cancelButtonText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.Neutral_Text_Color_Body,
  },
  addButtonText: {
    ...TypoSkin.buttonText3,
    color: '#fff',
  },
};

export default VideoModal;
