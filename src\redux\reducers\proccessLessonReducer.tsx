import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {CourseDA} from '../../modules/Course/da';
import {examDA} from '../../modules/exam/da';
import {randomGID} from '../../utils/Utils';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
import {StorageContanst} from '../../Config/Contanst';
import store from '../store/store';
export const FETCH_PROCCESS_LESSON_DATA = 'FETCH_PROCCESS_LESSON_DATA';
export const FETCH_PROCCESS_PART_DATA = 'FETCH_PROCCESS_PART_DATA';
export const UPDATE_PROCCESS_PART_DATA = 'UPDATE_PROCCESS_PART_DATA';
export const UPDATE_PROCESS = 'UPDATE_PROCESS';
export const SET_CURRENTSTEP = 'SET_CURRENTSTEP';
const initialState: {
  lessons: any[];
  // lstPart: any[];
  loading: boolean;
  success: boolean;
  error: any; // Hoặc để null|string nếu muốn cụ thể hơn
} = {
  lessons: [],
  // lstPart: [],
  loading: true,
  success: false,
  error: null,
};
export const proccessLessonSlice = createSlice({
  name: 'proccessLesson',
  initialState: initialState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case FETCH_PROCCESS_LESSON_DATA:
          state.lessons = action.payload.lessons;
          break;

        case UPDATE_PROCESS:
          const payload = action.payload.lessons;
          // Validate payload
          if (!payload) {
            console.error('Invalid payload for UPDATE_PROCESS:', payload);
            break;
          }

          const updateLesson = state.lessons.map((item: any) => {
            // Normalize ID comparison (convert to string to be safe)
            if (String(item.id) === String(payload.Id)) {
              const updatedSteps = item.steps.map((step: any) => {
                // Update only the specific step
                return {
                  ...step,
                  PercentCompleted: payload.PercentCompleted,
                };
              });
              return {
                ...item,
                steps: updatedSteps,
              };
            }
            return item;
          });
          state.lessons = updateLesson;
          break;
        case SET_CURRENTSTEP:
          return {
            ...state,
            lessons: state.lessons.map(lesson => ({
              ...lesson,
              steps: lesson.steps.map((step: any) => ({
                ...step,
                IsCurrentStep:
                  lesson.id === action.payload.lessons.lessonId &&
                  step.order === action.payload.lessons.stepOrder
                    ? true
                    : false,
              })),
            })),
          };
        default:
          break;
      }
      state.loading = false;
    },
    onFetching: state => {
      state.loading = true;
      state.lessons = [];
    },
  },
});
export default proccessLessonSlice.reducer;
const {handleActions, onFetching} = proccessLessonSlice.actions;
const courseDA = new CourseDA();
const examda = new examDA();
export class LessonActions {
  // Function mới - load dữ liệu theo cấu trúc Part-based
  static getProccessPartLesson = (id: string) => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    try {
      const response = await courseDA.getPartbyCourseId(id);
      if (response && response.data && response.data.length > 0) {
        const parts = response.data;
        //sort parts
        parts.sort((a: any, b: any) => a.Sort - b.Sort);
        const course = response.Course[0];
        if (parts.length === 0) {
          dispatch(
            handleActions({
              type: FETCH_PROCCESS_LESSON_DATA,
              lessons: [],
            }),
          );
          return;
        }

        // Lấy tất cả lesson IDs từ các parts để query progress
        const partIds = response.data.map((part: any) => part.Id);
        const allLessonsInParts = response.Lesson;
        const proccess = await courseDA.getProccessInPart(id, partIds);
        const roadmapSteps: any[] = [];
        let globalStepOrder = 0;

        // Xử lý từng Part
        for (let partIndex = 0; partIndex < parts.length; partIndex++) {
          const part = parts[partIndex];
          const lessonIds = part.LessonId?.split(',').filter((id: any) => id);
          const isLastPart = partIndex === parts.length - 1;
          // Lấy lessons trong part này
          const lessonsInPart = allLessonsInParts?.filter((lesson: any) =>
            lessonIds?.includes(lesson?.Id),
          );
          if (lessonsInPart && lessonsInPart) {
            for (
              let lessonIndex = 0;
              lessonIndex < lessonsInPart.length;
              lessonIndex++
            ) {
              const lesson = lessonsInPart[lessonIndex];
              globalStepOrder++;
              // Tìm progress của lesson này
              const lessonProgress = proccess?.find((p: any) =>
                p.LessonId?.includes(lesson?.Id),
              );
              // Tính toán progress tổng hợp cho lesson
              roadmapSteps.push({
                id: lesson.Id,
                title: lesson.Name,
                partName: `Chương ${partIndex + 1}: ${part.Name}`, // Tên chương
                isFirstStepOfPart: lessonIndex === 0, // Để hiển thị tên chương
                steps: [
                  {
                    type: 'Lesson',
                    displayName: lesson.Name,
                    order: globalStepOrder,
                    PercentCompleted: lessonProgress ? 100 : 0,
                    lessonId: lesson.Id,
                    partId: part.Id,
                    partName: part.Name,
                    stepIcons: 'color/development/book-open',
                    intro: lesson.Introduction,
                    video: lesson.Video,
                    document: lesson.Document,
                    quiz: lesson.Quiz,
                    flashcard: lesson.FlashCardId,
                  },
                ],
              });
            }
          }
          // Thêm ExamId step (Kiểm tra vượt rào) nếu có
          if (part.ExamId) {
            globalStepOrder++;
            roadmapSteps.push({
              id: `exam_${part.Id}`,
              title: 'Kiểm tra vượt rào',
              partName: part.Name,
              isFirstStepOfPart: false,
              steps: [
                {
                  type: 'Exam',
                  displayName: 'Kiểm tra vượt rào',
                  order: globalStepOrder,
                  id: part.ExamId,
                  PercentCompleted: proccess ? proccess[0]?.ExamPercent : 0, // TODO: Lấy từ exam progress
                  partId: part.Id,
                  partName: part.Name,
                  stepIcons: 'color/location/pennant',
                },
              ],
            });
          }

          // Thêm TestId step (Kiểm tra giữa kỳ/cuối kỳ) nếu có
          if (part.TestId) {
            globalStepOrder++;
            const testDisplayName = isLastPart
              ? 'Kiểm tra cuối kỳ'
              : 'Kiểm tra giữa kỳ';
            roadmapSteps.push({
              id: `test_${part.Id}`,
              title: testDisplayName,
              partName: part.Name,
              isFirstStepOfPart: false,
              steps: [
                {
                  type: 'Test',
                  displayName: testDisplayName,
                  order: globalStepOrder,
                  id: part.TestId,
                  PercentCompleted: proccess ? proccess[0]?.TestPercent : 0, // TODO: Lấy từ test progress
                  partId: part.Id,
                  partName: part.Name,
                  stepIcons: isLastPart
                    ? 'color/sport/chequered-flag'
                    : 'color/location/flag-complex',
                },
              ],
            });
          }
        }
        dispatch(
          handleActions({
            type: FETCH_PROCCESS_LESSON_DATA,
            lessons: roadmapSteps,
          }),
        );
      }
    } catch (error) {
      console.error('Error fetching proccess part lesson:', error);
      dispatch(
        handleActions({
          type: FETCH_PROCCESS_LESSON_DATA,
          lessons: [],
        }),
      );
    }
  };

  static updateProcess = (data: any) => async (dispatch: Dispatch) => {
    //lấy customerlesson theo courseId, lessonId, partId
    const customerLesson = await courseDA.checkExistsProccessLesson(
      data.CourseId,
      data.PartId,
    );
    if (customerLesson) {
      var obj1 = {};
      if (data.Type === 'Exam') {
        obj1 = {
          ExamPercent: 100,
          Id: customerLesson.Id,
        };
      }
      if (data.Type === 'Test') {
        obj1 = {
          TestPercent: 100,
          Id: customerLesson.Id,
        };
      }
      if (data.Type === 'Lesson') {
        var lessonids = customerLesson.LessonId?.split(',') ?? [];
        if (!lessonids.includes(data.lessonId)) {
          lessonids.push(data.lessonId);
          obj1 = {
            LessonId: lessonids.join(','),
            Id: customerLesson.Id,
          };
        }
      }
      const result = await courseDA.updateProccessLesson(obj1);
      if (result) {
        dispatch(
          handleActions({
            type: UPDATE_PROCESS,
            lessons: data,
          }),
        );
      }
    } else {
      //add
      var obj = {
        Id: randomGID(),
        CourseId: data.CourseId,
        PartId: data.PartId,
        CustomerId: store.getState().customer.data.Id,
        DateCreated: new Date().getTime(),
        Name: store.getState().customer.data.Name,
        LessonId: '',
        ExamPercent: 0,
        TestPercent: 0,
      };
      if (data.Type === 'Lesson') {
        obj.LessonId = data.lessonId;
      }
      if (data.Type === 'Exam') {
        obj.ExamPercent = 100;
      }
      if (data.Type === 'Test') {
        obj.TestPercent = 100;
      }
      const result = await courseDA.addProccessLesson(obj);
      if (result) {
        dispatch(
          handleActions({
            type: UPDATE_PROCESS,
            lessons: data,
            Id: data.lessonId,
          }),
        );
      }
    }
  };
  static setCurrentStep = (lesson: any) => async (dispatch: Dispatch) => {
    dispatch(
      handleActions({
        type: SET_CURRENTSTEP,
        lessons: lesson,
      }),
    );
  };
}
