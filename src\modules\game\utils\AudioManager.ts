import Sound from 'react-native-sound';

export interface AudioState {
  isPlaying: boolean;
  isLoading: boolean;
  currentTrack: string | null;
}

class AudioManager {
  private static instance: AudioManager;
  private sound: Sound | null = null;
  private currentState: AudioState = {
    isPlaying: false,
    isLoading: false,
    currentTrack: null,
  };
  private listeners: ((state: AudioState) => void)[] = [];

  private constructor() {
    // Enable playback in silence mode (iOS)
    Sound.setCategory('Playback');
  }

  public static getInstance(): AudioManager {
    if (!AudioManager.instance) {
      AudioManager.instance = new AudioManager();
    }
    return AudioManager.instance;
  }

  public subscribe(listener: (state: AudioState) => void): () => void {
    this.listeners.push(listener);
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentState));
  }

  private updateState(newState: Partial<AudioState>): void {
    this.currentState = { ...this.currentState, ...newState };
    this.notifyListeners();
  }

  public async initializeAudio(audioPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // Don't reinitialize if same track is already loaded
      if (this.currentState.currentTrack === audioPath && this.sound) {
        resolve();
        return;
      }

      this.updateState({ isLoading: true });

      // Release previous audio if exists
      if (this.sound) {
        this.sound.stop();
        this.sound.release();
        this.sound = null;
      }

      // Create new audio instance
      this.sound = new Sound(audioPath, Sound.MAIN_BUNDLE, (error) => {
        this.updateState({ isLoading: false });
        
        if (error) {
          console.error('AudioManager: Failed to load audio:', error);
          this.updateState({ currentTrack: null });
          reject(error);
          return;
        }

        console.log('AudioManager: Audio loaded successfully');
        this.updateState({ currentTrack: audioPath });
        resolve();
      });
    });
  }

  public async playAudio(loop: boolean = false): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.sound) {
        const error = 'No audio loaded';
        reject(new Error(error));
        return;
      }

      // Don't play if already playing
      if (this.currentState.isPlaying) {
        resolve();
        return;
      }

      this.updateState({ isPlaying: true });

      // Set loop if requested
      if (loop) {
        this.sound.setNumberOfLoops(-1); // -1 means infinite loop
      }

      this.sound.play((success) => {
        if (!loop && success) {
          this.updateState({ isPlaying: false });
        }
        
        if (success) {
          console.log('AudioManager: Audio played successfully');
          resolve();
        } else {
          this.updateState({ isPlaying: false });
          reject(new Error('Audio playback failed'));
        }
      });
    });
  }

  public pauseAudio(): void {
    if (this.sound && this.currentState.isPlaying) {
      this.sound.pause(() => {
        this.updateState({ isPlaying: false });
        console.log('AudioManager: Audio paused');
      });
    }
  }

  public resumeAudio(): void {
    if (this.sound && !this.currentState.isPlaying) {
      this.sound.play((success) => {
        if (success) {
          this.updateState({ isPlaying: true });
          console.log('AudioManager: Audio resumed');
        }
      });
    }
  }

  public stopAudio(): void {
    if (this.sound) {
      this.sound.stop(() => {
        this.updateState({ isPlaying: false });
        console.log('AudioManager: Audio stopped');
      });
    }
  }

  public clearAudio(): void {
    if (this.sound) {
      this.sound.stop(() => {
        this.sound?.release();
        this.sound = null;
        this.updateState({ 
          isPlaying: false, 
          currentTrack: null 
        });
        console.log('AudioManager: Audio cleared completely');
      });
    }
  }

  public setVolume(volume: number): void {
    if (this.sound) {
      this.sound.setVolume(Math.max(0, Math.min(1, volume)));
    }
  }

  public getState(): AudioState {
    return { ...this.currentState };
  }

  public forceCleanup(): void {
    try {
      Sound.setActive(false);
      if (this.sound) {
        this.sound.stop();
        this.sound.release();
        this.sound = null;
      }
      this.updateState({ 
        isPlaying: false, 
        isLoading: false, 
        currentTrack: null 
      });
      console.log('AudioManager: Force cleanup completed');
    } catch (error) {
      console.error('AudioManager: Error during force cleanup:', error);
    }
  }
}

export default AudioManager;
