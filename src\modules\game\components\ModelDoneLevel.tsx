import React, {useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import {useSelector} from 'react-redux';
import {useGameHook} from '../../../redux/hook/gameHook';
import {RootState} from '../../../redux/store/store';
import {GameStatus} from '../../../Config/Contanst';
import {GameDA} from '../gameDA';
const gameDa = new GameDA();

const {width, height} = Dimensions.get('window');

interface ModelDoneLevelProps {
  visible: boolean;
  onNextLevel: () => void;
  message?: string;
  gameId: string;
  competenceId: string;
  gameConfig: any,
  currentMilestoneId: any
}

const ModelDoneLevel = ({
  visible,
  onNextLevel,
  message = 'Winner',
  gameId,
  competenceId,
  gameConfig,
  currentMilestoneId
}: ModelDoneLevelProps) => {
  const {gem} = useSelector((state: RootState) => state.gameStore);
  const gameHook = useGameHook();
  useEffect(() => {
    if (visible) {
      if (!currentMilestoneId) {
        return;
      }
      //tính điểm cho người chơi
      debugger
      insertScore();
    }
  }, [visible, currentMilestoneId]);

  const insertScore = async () => {
    debugger
    const result = await gameDa.insertScore({
      milestoneId: currentMilestoneId,
      gameId: gameId,
      competenceId: competenceId,
      status: GameStatus.Completed,
      score: gameConfig.Score || gameConfig.score  || 0,
      name: `VuotCap_${currentMilestoneId}`,
    });
    if (result) {
      gameHook.setData({stateName: 'gem', value: gem + (gameConfig.Score || gameConfig.score  || 0)});
    }
  };
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      statusBarTranslucent={true}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.iconContainer}>
            <Text style={styles.messageText}>{message}</Text>
            <View style={{height: 50}}></View>
            {/* <View style={styles.pointContainer}>
              <View style={[styles.icon, styles.gemIcon]}>
                <Image source={require('../assets/big_gem.png')} />
              </View>
              <Text style={styles.textIcon}>
                {score}+{totalScore}
              </Text>
            </View>
            <View style={styles.scoreItem}>
              <Image
                source={require('../ailatrieuphu/assets/rank.png')}
                style={styles.diamondIcon}
              />
              {loadingRanking ? (
                <ActivityIndicator size="small" color="#FFD700" />
              ) : (
                <>
                  <Text style={styles.scoreText}>
                    {rankingInfo?.afterRanking?.rank}
                  </Text>
                  {rankingInfo?.afterRanking?.previousRank &&
                    rankingInfo?.afterRanking?.rank <
                      rankingInfo?.afterRanking?.previousRank && (
                      <Text style={styles.rankUpText}>
                        <Winicon
                          src={'color/arrows/triangle-up'}
                          size={9}
                          color="#4CAF50"
                        />{' '}
                        {rankingInfo?.afterRanking?.previousRank -
                          rankingInfo?.afterRanking?.rank}
                      </Text>
                    )}
                </>
              )}
            </View> */}
          </View>

          <View style={styles.birdContainer}>
            <Image source={require('../assets/winner_bird.png')} />
          </View>

          <TouchableOpacity style={styles.restartButton} onPress={onNextLevel}>
            <Image
              source={require('../assets/next_button.png')}
              style={styles.restartButton}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.69)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContent: {
    width: width * 0.93,
    minHeight: height * 0.8,
    padding: 20,
    marginBottom: 20,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  iconContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  rankUpText: {
    fontSize: 12,
    marginLeft: 10,
    fontWeight: 'bold',
    color: '#4CAF50', // Màu xanh lá cây
    textAlign: 'center',
  },
  scoreItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 20,
    height: 30,
    justifyContent: 'center',
    position: 'relative',
    marginBottom: 20,
    marginTop: 20,
    paddingHorizontal: 10,
    paddingLeft: 20,
  },
  diamondIcon: {
    marginRight: 10,
    borderRadius: 15,
    width: 40,
    height: 47,
    position: 'absolute',
    left: -20,
    top: -10,
  },
  scoreText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 10,
  },
  messageText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FDE832',
  },
  birdContainer: {
    marginVertical: 30,
    position: 'relative',
  },
  restartButton: {
    width: 230,
    height: 90,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pointContainer: {
    position: 'relative',
    backgroundColor: 'white',
    width: 130,
    marginLeft: 24,
    marginTop: 40,
    height: 32,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cupContainer: {
    marginTop: 36,
  },
  icon: {
    position: 'absolute',
    zIndex: 1,
    left: 0,
    transform: [{translateX: -10}, {translateY: -3}],
    alignItems: 'center',
    justifyContent: 'center',
    width: 25,
    height: 25,
  },
  gemIcon: {
    top: 6,
  },
  cupIcon: {
    top: 0,
  },
  textIcon: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default ModelDoneLevel;
