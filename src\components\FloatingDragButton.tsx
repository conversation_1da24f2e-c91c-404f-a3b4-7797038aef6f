import React, {useRef, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Animated,
} from 'react-native';
import {
  PanGestureHandler,
  State,
} from 'react-native-gesture-handler';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../assets/skin/colors';
import { Image } from 'react-native';
import FastImage from '@d11/react-native-fast-image';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

interface FloatingDragButtonProps {
  onPress?: () => void;
  icon?: string;
  size?: number;
  backgroundColor?: string;
  iconColor?: string;
  onHide?: () => void;
  animationType?: 'bounce' | 'rotate' | 'bounceRotate' | 'none';
  animationInterval?: number; // in milliseconds
}

const FloatingDragButton: React.FC<FloatingDragButtonProps> = ({
  onPress,
  onHide,
  size = 100,
  backgroundColor = 'transparent',
  animationType = 'bounce',
  animationInterval = 5000,
}) => {
  // Sử dụng 2 animated values riêng biệt cho base position và translation
  // Vị trí ban đầu: gần bottom navigator (cách bottom ~150px để tránh che tab bar)
  const baseX = useRef(new Animated.Value(screenWidth - size - 20)).current;
  const baseY = useRef(new Animated.Value(screenHeight - size - 150)).current;
  const translationX = useRef(new Animated.Value(0)).current;
  const translationY = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(1)).current;

  // Animation values
  const bounceY = useRef(new Animated.Value(0)).current;
  const rotation = useRef(new Animated.Value(0)).current;

  const isDragging = useRef(false);
  const animationTimer = useRef<NodeJS.Timeout | null>(null);

  // Animation functions
  const startBounceAnimation = () => {
    Animated.sequence([
      Animated.timing(bounceY, {
        toValue: -15,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(bounceY, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(bounceY, {
        toValue: -10,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(bounceY, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const startRotateAnimation = () => {
    Animated.sequence([
      Animated.timing(rotation, {
        toValue: 0.2, // ~11 degrees
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(rotation, {
        toValue: -0.2, // ~-11 degrees
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.timing(rotation, {
        toValue: 0.1, // ~6 degrees
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(rotation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const startBounceRotateAnimation = () => {
    // First do bounce animation
    Animated.sequence([
      // Bounce part
      Animated.timing(bounceY, {
        toValue: -15,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(bounceY, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(bounceY, {
        toValue: -10,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(bounceY, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      // Small pause between bounce and rotate
      Animated.delay(200),
    ]).start(() => {
      // Then do rotate animation
      Animated.sequence([
        Animated.timing(rotation, {
          toValue: 0.2, // ~11 degrees
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(rotation, {
          toValue: -0.2, // ~-11 degrees
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(rotation, {
          toValue: 0.1, // ~6 degrees
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(rotation, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    });
  };

  const startAnimation = () => {
    if (animationType === 'bounce') {
      startBounceAnimation();
    } else if (animationType === 'rotate') {
      startRotateAnimation();
    } else if (animationType === 'bounceRotate') {
      startBounceRotateAnimation();
    }
  };

  // Setup animation timer
  useEffect(() => {
    if (animationType !== 'none') {
      animationTimer.current = setInterval(() => {
        if (!isDragging.current) {
          startAnimation();
        }
      }, animationInterval);
    }

    return () => {
      if (animationTimer.current) {
        clearInterval(animationTimer.current);
      }
    };
  }, [animationType, animationInterval]);

  const onGestureEvent = Animated.event(
    [
      {
        nativeEvent: {
          translationX: translationX,
          translationY: translationY,
        },
      },
    ],
    {useNativeDriver: true},
  );

  const onHandlerStateChange = (event: any) => {
    const {state, translationX: tx, translationY: ty} = event.nativeEvent;

    if (state === State.BEGAN) {
      isDragging.current = true;

      // Scale up slightly when dragging starts
      Animated.spring(scale, {
        toValue: 1.1,
        useNativeDriver: true,
      }).start();
    }

    if (state === State.END || state === State.CANCELLED) {
      isDragging.current = false;

      // Scale back to normal
      Animated.spring(scale, {
        toValue: 1,
        useNativeDriver: true,
      }).start();

      // Get current base position
      const currentBaseX = (baseX as any)._value;
      const currentBaseY = (baseY as any)._value;

      // Calculate new base position
      const newX = currentBaseX + tx;
      const newY = currentBaseY + ty;

      // Constrain to screen bounds
      // Top: 50px from top, Bottom: 120px from bottom (để không che tab bar)
      const constrainedX = Math.max(
        10,
        Math.min(screenWidth - size - 10, newX),
      );
      const constrainedY = Math.max(
        50,
        Math.min(screenHeight - size - 120, newY),
      );

      // Update base position and reset translation
      baseX.setValue(constrainedX);
      baseY.setValue(constrainedY);
      translationX.setValue(0);
      translationY.setValue(0);
    }
  };

  const handlePress = () => {
    if (!isDragging.current && onPress) {
      console.log('Floating button pressed!');
      onPress();
    }
  };

  return (
    <View style={styles.container} pointerEvents="box-none">
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}>
        <Animated.View
          style={[
            styles.button,
            {
              width: size,
              height: size,
              borderRadius: size / 2,
              backgroundColor,
              transform: [
                {translateX: Animated.add(baseX, translationX)},
                {translateY: Animated.add(Animated.add(baseY, translationY), bounceY)},
                {scale},
                {rotate: rotation.interpolate({
                  inputRange: [-1, 1],
                  outputRange: ['-57.3deg', '57.3deg'], // Convert radians to degrees
                })},
              ],
            },
          ]}>
          <TouchableOpacity
            style={styles.touchable}
            onPress={handlePress}
            activeOpacity={0.8}>
            <FastImage
              source={require('../assets/contact.png')}
              style={styles.contactImage}
              resizeMode={FastImage.resizeMode.contain}
            />
          </TouchableOpacity>

          {/* Close button */}
          <TouchableOpacity
            style={[styles.closeButton, {
              top: -2,
              right: -2,
              width: size * 0.2,
              height: size * 0.2,
              borderRadius: (size * 0.2) / 2,
            }]}
            onPress={onHide}
            activeOpacity={0.8}>
            <Winicon
              src="outline/layout/xmark"
              size={size * 0.15}
              color="white"
            />
          </TouchableOpacity>
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
  },
  button: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  touchable: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactImage: {
    width: '100%',
    height: '100%',
    borderRadius: 42, // Half of size (84/2)
  },
  closeButton: {
    position: 'absolute',
    backgroundColor: '#6e6a6ac7',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default FloatingDragButton;
