import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';

// Position Indicator Component - hiển thị vị trí có thể chèn từ
interface PositionIndicatorProps {
  index: number;
  isVisible: boolean;
  onPress: (index: number) => void;
}

export const PositionIndicator: React.FC<PositionIndicatorProps> = ({
  index,
  isVisible,
  onPress,
}) => {
  if (!isVisible) return null;

  return (
    <TouchableOpacity
      style={styles.positionIndicator}
      onPress={() => onPress(index)}
      activeOpacity={0.7}>
      <View style={styles.positionDot} />
    </TouchableOpacity>
  );
};

// Tappable Word In Sentence - từ trong câu có thể tap để select
interface TappableWordInSentenceProps {
  word: string;
  index: number;
  isSelected: boolean;
  isSubmitted: boolean;
  isError: boolean;
  onPress: (word: string, index: number) => void;
  onRemove: (word: string, index: number) => void;
}

export const TappableWordInSentence: React.FC<TappableWordInSentenceProps> = ({
  word,
  index,
  isSelected,
  isSubmitted,
  isError,
  onPress,
  onRemove,
}) => {
  const handlePress = () => {
    if (isSubmitted) return;
    onPress(word, index);
  };

  const handleLongPress = () => {
    if (isSubmitted) return;
    onRemove(word, index);
  };

  return (
    <TouchableOpacity
      style={[
        styles.wordInSentence,
        isSelected && styles.selectedWord,
        isError && styles.errorWord,
        isSubmitted && styles.submittedWord,
      ]}
      onPress={handlePress}
      onLongPress={handleLongPress}
      activeOpacity={0.7}
      disabled={isSubmitted}>
      <Text
        style={[
          styles.wordText,
          isSelected && styles.selectedWordText,
          isError && styles.errorWordText,
        ]}>
        {word}
      </Text>
    </TouchableOpacity>
  );
};

// Tappable Word In Bank - từ trong ngân hàng từ có thể tap để select
interface TappableWordInBankProps {
  word: string;
  index: number;
  isSelected: boolean;
  onPress: (word: string, index: number) => void;
}

export const TappableWordInBank: React.FC<TappableWordInBankProps> = ({
  word,
  index,
  isSelected,
  onPress,
}) => {
  const handlePress = () => {
    onPress(word, index);
  };

  return (
    <TouchableOpacity
      style={[
        styles.wordInBank,
        isSelected && styles.selectedWordBank,
      ]}
      onPress={handlePress}
      activeOpacity={0.7}>
      <Text
        style={[
          styles.wordBankText,
          isSelected && styles.selectedWordBankText,
        ]}>
        {word}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  positionIndicator: {
    width: 30,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
  },
  positionDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#4CAF50',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  wordInSentence: {
    backgroundColor: '#F1D1A6',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 4,
    marginVertical: 2,
    borderWidth: 1,
    borderColor: '#FFFFFFFF',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedWord: {
    backgroundColor: '#FFF3E0',
    borderColor: '#FF9800',
    borderWidth: 3,
  },
  errorWord: {
    backgroundColor: '#FFEBEE',
    borderColor: '#F44336',
  },
  submittedWord: {
    opacity: 0.7,
  },
  wordText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#112164',
    textAlign: 'center',
  },
  selectedWordText: {
    color: '#E65100',
  },
  errorWordText: {
    color: '#C62828',
  },
  wordInBank: {
    backgroundColor: '#F1D1A6',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 4,
    marginVertical: 4,
    borderWidth: 1,
    borderColor: '#FFFFFFFF',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedWordBank: {
    backgroundColor: '#FFF3E0',
    borderColor: '#FF9800',
    borderWidth: 3,
  },
  wordBankText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#112164',
    textAlign: 'center',
  },
  selectedWordBankText: {
    color: '#E65100',
  },
});
