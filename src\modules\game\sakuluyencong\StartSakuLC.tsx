import {
  ImageBackground,
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Vibration,
  BackHandler,
} from 'react-native';
import Sound from 'react-native-sound';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import CountBadge from '../components/CountQuestions';
import {useCallback, useEffect, useRef, useState} from 'react';
import {BottomGame} from '../components/BottomGame';
import {SafeAreaView} from 'react-native-safe-area-context';
import store, {RootState} from '../../../redux/store/store';
import {useSelector} from 'react-redux';

import GameOverModal from '../components/GameOverModel';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSakuLCHook} from '../../../redux/hook/game/sakuLCHook';
import {SakuLCWord} from './types/sakuLCTypes';
import ConfigAPI from '../../../Config/ConfigAPI';
import React from 'react';
import {useFocusEffect, useNavigation, useRoute} from '@react-navigation/native';
import WinnerModal from './components/WinnerModal';
import {GameDA} from '../gameDA';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import {randomGID} from '../../../utils/Utils';
import {DataController} from '../../../base/baseController';
import GamePauseModal from '../components/ModelPauseGame';
import {hasMaxSort, checkSakuLCAnswer} from '../utils/functions';
import { useGameAudio } from '../ailatrieuphu/hooks/useGameAudio';

// Styles moved up to be accessible by DraggableWord
const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  gameInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 15,
  },
  lives: {
    fontSize: 16,
  },
  score: {
    backgroundColor: '#4CAF50',
    color: 'white',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    fontSize: 12,
    fontWeight: 'bold',
  },
  instruction: {
    backgroundColor: '#FCF8E8',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dropZone: {
    backgroundColor: 'white',
    borderRadius: 10,
    minHeight: 80,
    marginBottom: 20,
    padding: 15,
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  dropZoneContent: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
  },
  dropZoneHint: {
    color: '#999',
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
    marginTop: 10,
  },
  checkButton: {
    backgroundColor: '#D32F2F',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    alignSelf: 'center',
    marginBottom: 30,
  },
  checkButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  wordsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 30,
  },
  wordContainer: {
    margin: 6,
    backgroundColor: '#FCF8E8',
    borderRadius: 10,
    shadowColor: '#32325D',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wordText: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
  },
  questionText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
    flex: 1,
    textAlign: 'center',
  },
  controlButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  controlButton: {
    backgroundColor: '#FF6B35',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonText: {
    fontSize: 20,
  },
  errorText: {
    color: '#F44336',
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#112164',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
    paddingHorizontal: 20,
  },
  feedbackContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: 'white',
    borderRadius: 8,
    alignItems: 'center',
  },
  feedbackText: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  successText: {
    color: '#4CAF50',
  },
  questionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flexWrap: 'nowrap',
    width: '100%',
  },
  audioButton: {
    marginRight: 10,
    padding: 6,
    borderRadius: 18,
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
  },
  audioIcon: {
    fontSize: 18,
  },
  disabledButton: {
    backgroundColor: '#cccccc',
    opacity: 0.6,
  },
  disabledButtonText: {
    color: '#666666',
  },
});

// TappableWord component - simple tap to add to drop zone
interface TappableWordProps {
  word: SakuLCWord;
  onAddWordToDropZone: (word: SakuLCWord) => void;
  wordsInDropZone: SakuLCWord[];
  isPauseGame: boolean;
}

const TappableWord = React.memo(
  ({
    word,
    onAddWordToDropZone,
    wordsInDropZone,
    isPauseGame,
  }: TappableWordProps) => {
    // Don't render if word is already in drop zone
    if (wordsInDropZone.find(w => w.id === word.id)) {
      return null;
    }

    return (
      <TouchableOpacity
        style={[
          styles.wordContainer,
          isPauseGame && {opacity: 0.5}
        ]}
        onPress={isPauseGame ? undefined : () => onAddWordToDropZone(word)}
        disabled={isPauseGame}
        activeOpacity={0.7}>
        <Text style={styles.wordText}>{word.text}</Text>
      </TouchableOpacity>
    );
  },
);

const StartSakuLC = () => {
  const {isGameOver, messageGameOver, isRunTime, gem, gemCost} = useSelector(
    (state: RootState) => state.gameStore,
  );
  const {
    availableWords,
    questionDone,
    totalQuestion,
    currentQuestion,
    loading,
    error,
    usedHints,
    configLoading,
    configError,
    initialized,
    configInitialized,
    feedbackMessage,
    feedbackType,
    isAnswerCorrect,
    wordsInDropZone: reduxWordsInDropZone,
    // Sử dụng config data từ API
    maxLives,
    currentLives,
    timeLimit,
    timeRemaining,
    gemHint,
  } = useSelector((state: RootState) => state.SakuLC);

  const [wordsInDropZone, setWordsInDropZone] = useState<SakuLCWord[]>([]);
  const [audioPlayer, setAudioPlayer] = useState<Sound | null>(null);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [showWinnerModal, setShowWinnerModal] = useState(false);
  const sakuLCHook = useSakuLCHook();
  const gameHook = useGameHook();
  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);
  //navigation
  const navigation = useNavigation<any>();
  const hasHint = currentQuestion?.hint && currentQuestion.hint.trim() !== '';
  const isHintUsed = usedHints.includes(currentQuestion?.id?.toString() || '');
  const shouldShowHintButton = Boolean(hasHint && !isHintUsed);
  const [isPauseGame, setIsPauseGame] = useState(false);
const {
    audioState,
    playCorrectAnswer,
    playInCorrectAnswer,
    playWin,
    playGameOver,
    playTimeWarning,
    stopCurrentSound,
    stopTimeWarning,
    clearAllSounds,
    toggleMute,
  } = useGameAudio();
  useFocusEffect(
    useCallback(() => {
      console.log('StartDHBC focused');

      return () => {
        console.log('StartDHBC blurred - clearing all sounds');
        clearAllSounds();
      };
    }, [clearAllSounds]),
  );
  // Handle hardware back button
  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        console.log('DHBC: Hardware back button pressed');
        clearAllSounds();
        return false; // Let default behavior handle navigation
      },
    );
    return () => backHandler.remove();
  }, [clearAllSounds]);
  // Handle adding word to drop zone
  const handleAddWordToDropZone = (wordToAdd: SakuLCWord) => {
    setWordsInDropZone(prev => {
      if (!prev.find(w => w.id === wordToAdd.id)) {
        removeWordFromAvailableWords(wordToAdd);
        return [...prev, wordToAdd];
      }
      return prev;
    });
  };

  // Render available words directly
  const renderAvailableWords = () => {
    return availableWords.map((word: SakuLCWord) => (
      <TappableWord
        key={word.id}
        word={word}
        onAddWordToDropZone={handleAddWordToDropZone}
        wordsInDropZone={wordsInDropZone}
        isPauseGame={isPauseGame}
      />
    ));
  };
  //router param
  const route = useRoute<any>();
  const {competenceId, milestoneId} = route.params || {
    competenceId: '1',
    milestoneId: 1,
  };

  // Load game data on component mount
  useEffect(() => {
    initializeGameData();
    fetchScore();
  }, [competenceId, milestoneId]);
  const fetchScore = async () => {
    try {
      // Lấy thông tin điểm từ bảng GameCUstomer
      const gameDa = new GameDA();
      const result = await gameDa.getScoreByCustomerIdAndGameId(
        store.getState().customer.data.Id,
        ConfigAPI.gameSakuLC,
      );
      gameHook.setData({stateName: 'gem', value: result ?? 0});
    } catch (error) {
      console.error('Lỗi khi lấy thông tin điểm:', error);
    }
  };
  // Update local state when Redux state changes
  useEffect(() => {
    setWordsInDropZone(reduxWordsInDropZone);
  }, [reduxWordsInDropZone]);

  // No longer need to measure drop zone for tap-based interaction

  useEffect(() => {
    if (currentLives < 1) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);

  // Auto next question when answer is correct
  useEffect(() => {
    if (isAnswerCorrect === true) {
      if (questionDone + 1 >= totalQuestion && totalQuestion > 0) {
        playWin();
        setTimeout(() => {
          setShowWinnerModal(true);
          gameHook.setData({stateName: 'isRunTime', value: false}); // Dừng timer
        }, 500); // Delay nhỏ để animation hoàn thành
      }

      setTimeout(() => {
        sakuLCHook.nextQuestion();
        sakuLCHook.clearFeedback();
      }, 1000); // Show success message for 1.5 seconds
    }
  }, [isAnswerCorrect]);

  // Start timer when game is initialized and config is loaded
  useEffect(() => {
    if (initialized && configInitialized && timeLimit > 0) {
      console.log('===== STARTING TIMER =====');
      console.log('timeLimit:', timeLimit);
      sakuLCHook.startTimer();
      gameHook.setData({stateName: 'isRunTime', value: true});
      gameHook.setData({stateName: 'time', value: timeLimit});
      gameHook.setData({stateName: 'gemCost', value: gemHint || 0});
    }
  }, [initialized, configInitialized, timeLimit]);

  // Timer countdown
  useEffect(() => {
    let interval: NodeJS.Timeout;

    // Chỉ chạy timer khi game không bị pause và các điều kiện khác đều thỏa mãn
    if (
      initialized &&
      configInitialized &&
      timeLimit > 0 &&
      timeRemaining > 0 &&
      !isPauseGame &&
      isRunTime
    ) {
      interval = setInterval(() => {
        sakuLCHook.updateTimer();
        // Sync with global game state for HeadGame component
        gameHook.setData({stateName: 'time', value: timeRemaining - 1});
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [initialized, configInitialized, timeLimit, timeRemaining, isPauseGame, isRunTime]);

  // Game over when time runs out
  useEffect(() => {
    if (timeRemaining <= 0 && initialized && configInitialized && timeLimit > 0 && isRunTime) {
      gameOver('Hết giờ rồi, làm lại nào');
    }
  }, [timeRemaining, initialized, configInitialized, timeLimit, isRunTime]);

  // Setup audio when question changes
  useEffect(() => {
    if (currentQuestion?.audioUrl) {
      const url = ConfigAPI.getValidLink(currentQuestion.audioUrl);
      setupAudio(url);
    }

    // Cleanup previous audio
    return () => {
      if (audioPlayer) {
        audioPlayer.stop();
        audioPlayer.release();
      }
    };
  }, [currentQuestion]);

  // Setup audio player
  const setupAudio = (audioUrl: string) => {
    console.log('===== SETTING UP AUDIO =====');
    console.log('Audio URL:', audioUrl);
    // Release previous audio
    if (audioPlayer) {
      audioPlayer.stop();
      audioPlayer.release();
    }

    // Enable playback in silence mode (iOS)
    Sound.setCategory('Playback');

    // Create new audio instance
    const sound = new Sound(audioUrl, '', audioError => {
      if (audioError) {
        console.error('Failed to load audio:', audioError);
        return;
      }
      console.log('Audio loaded successfully');
      setAudioPlayer(sound);
    });
  };

  // Play audio function
  const playAudio = () => {
    console.log('===== PLAY AUDIO CALLED =====');
    console.log('Audio player:', audioPlayer);
    console.log('Is playing:', isPlayingAudio);

    if (!audioPlayer) {
      console.log('No audio player available');
      return;
    }

    if (isPlayingAudio) {
      // Stop audio if currently playing
      audioPlayer.stop(() => {
        console.log('Audio stopped');
        setIsPlayingAudio(false);
      });
    } else {
      // Play audio
      setIsPlayingAudio(true);
      audioPlayer.play(success => {
        console.log('Audio play finished, success:', success);
        setIsPlayingAudio(false);
      });
    }
  };

  const initializeGameData = async () => {
    try {
      console.log('===== STARTING initializeGameData =====');
      console.log('GameId:', ConfigAPI.gameSakuLC);
      console.log('MilestoneId:', milestoneId);
      console.log('CompetenceId:', competenceId);
      // Load game config
      console.log('===== CALLING loadGameConfig =====');
      await sakuLCHook.loadGameConfig(ConfigAPI.gameSakuLC);
      console.log('===== loadGameConfig COMPLETED =====');

      // Load questions
      console.log('===== CALLING loadQuestions =====');
      await sakuLCHook.loadQuestions(
        ConfigAPI.gameSakuLC,
        milestoneId,
        competenceId,
      );
      console.log('===== loadQuestions COMPLETED =====');

      // Initialize game
      console.log('===== CALLING initializeGame =====');
      sakuLCHook.initializeGame();
      console.log('===== initializeGame COMPLETED =====');
    } catch (err) {
      console.error('===== ERROR in initializeGameData:', err);
    }
  };

  const restartGame = () => {
    sakuLCHook.clearFeedback();
    sakuLCHook.reset();
    gameHook.restartGame();

    // Reset pause state khi restart
    setIsPauseGame(false);

    if (configInitialized && timeLimit) {
      console.log(
        `[StartSakuLC] Syncing global timer with SakuLC config: ${timeLimit}s`,
      );
      gameHook.setData({stateName: 'time', value: timeLimit});
      gameHook.setData({stateName: 'isRunTime', value: true});

      // Sync lives với SakuLC config - sử dụng sakuLCHook thay vì gameHook
      if (maxLives) {
        console.log(`[StartSakuLC] Resetting lives to maxLives: ${maxLives}`);
        sakuLCHook.setData({stateName: 'maxLives', value: maxLives});
        sakuLCHook.setData({stateName: 'currentLives', value: maxLives});
      }
    } else {
      // Fallback nếu config chưa load
      gameHook.resetGame();
    }
  };

  const togglePauseGame = () => {
    console.log(
      `[StartSakuLC] Toggle pause game: ${isPauseGame ? 'Resume' : 'Pause'}`,
    );

    if (!isPauseGame) {
      // Pause game
      gameHook.setData({stateName: 'isRunTime', value: false});
      setIsPauseGame(true);

      // Pause audio nếu đang phát
      if (isPlayingAudio && audioPlayer) {
        audioPlayer.pause();
        setIsPlayingAudio(false);
      }
    } else {
      // Resume game
      gameHook.setData({stateName: 'isRunTime', value: true});
      setIsPauseGame(false);
    }
  };

  const gameOver = (message: string) => {
    playGameOver();
    gameHook.gameOver(message);
  };

  const onErrorQuestion = () => {
    Vibration.vibrate([0, 500, 200, 500]);

    // Số từ không đúng -> sai -> trừ mạng
    sakuLCHook.setData({
      stateName: 'feedbackMessage',
      value: 'Đáp án sai rồi. hãy thử lại',
    });
    sakuLCHook.setData({
      stateName: 'feedbackType',
      value: 'error',
    });
    sakuLCHook.setData({
      stateName: 'isAnswerCorrect',
      value: false,
    });

    // Trừ mạng
    sakuLCHook.setData({
      stateName: 'currentLives',
      value: currentLives - 1,
    });
  };

  // No longer need to find drop zone position for tap-based interaction

  // Kiểm tra đáp án
  const checkAnswer = () => {
    console.log('===== CHECK ANSWER CALLED =====');
    console.log('Words in drop zone:', wordsInDropZone);
    console.log('Current question words:', currentQuestion?.words);

    // Kiểm tra điều kiện cơ bản
    if (!currentQuestion || wordsInDropZone.length === 0) {
      console.log('No current question or no words in drop zone');
      playInCorrectAnswer();
      return onErrorQuestion();
    }

    // Lấy tất cả từ đúng (IsResult = true) từ câu hỏi hiện tại để tham khảo
    const correctWords = currentQuestion.words.filter(word => word.IsResult === true);
    console.log('Total correct words available:', correctWords.length);
    console.log('Words in drop zone:', wordsInDropZone.length);

    // Sử dụng logic check mới cho SakuLC
    // Logic này sẽ kiểm tra:
    // 1. Tất cả từ trong drop zone phải có IsResult = true (không có từ nhiễu)
    // 2. Thứ tự Sort phải tăng dần theo vị trí user sắp xếp
    const isCorrect = checkSakuLCAnswer(wordsInDropZone);
    console.log('Answer is correct:', isCorrect);

    if (isCorrect) {
      playCorrectAnswer();
      sakuLCHook.setData({
        stateName: 'feedbackMessage',
        value: 'Đáp án đúng rồi. Chúc mừng bạn',
      });
      sakuLCHook.setData({
        stateName: 'feedbackType',
        value: 'success',
      });
      sakuLCHook.setData({
        stateName: 'isAnswerCorrect',
        value: true,
      });
    } else {
      playInCorrectAnswer();
      onErrorQuestion();
    }
  };

  // Xoá text khỏi drop zone
  const removeWordFromDropZone = (word: SakuLCWord) => {
    sakuLCHook.setData({
      stateName: 'availableWords',
      value: [...availableWords, word],
    });
    setWordsInDropZone(prev => prev.filter(w => w.id !== word.id));
  };

  // Xoá text khỏi available words
  const removeWordFromAvailableWords = (word: SakuLCWord) => {
    sakuLCHook.setData({
      stateName: 'availableWords',
      value: availableWords.filter(w => w.id !== word.id),
    });
  };

  // Render text trong drop zone
  const renderWordsInDropZone = () => {
    return wordsInDropZone.map(word => (
      <TouchableOpacity
        key={word.id}
        style={[styles.wordContainer, isPauseGame && {opacity: 0.5}]}
        onPress={isPauseGame ? undefined : () => removeWordFromDropZone(word)}
        disabled={isPauseGame}>
        <Text style={styles.wordText}>{word.text}</Text>
      </TouchableOpacity>
    ));
  };

  const useHint = () => {
    if (gem < (gemHint || 5)) {
      // show model thông báo không đủ gem
      Alert.alert(
        'Thông báo',
        'Bạn không đủ Sakupi để sử dụng gợi ý',
        [
          {
            text: 'OK',
            style: 'cancel',
          },
        ],
        {cancelable: false},
      );
      return;
    }
    gameHook.setData({
      stateName: 'gem',
      value: gem - (gemHint || 0),
    });
    setShowModelConfirm(false);
    if (gemHint > 0) {
      updateScore(gemHint || 0);
    }
    setShowHintModel(true);
    console.log('Hint feature temporarily disabled - no hint data from API');
  };
  const updateScore = async (score: number) => {
    if (score <= 0) return;
    const gamecustomerController = new DataController('GameCustomer');
    const customerId = store.getState().customer.data.Id;
    const game = {
      Id: randomGID(),
      CustomerId: customerId,
      GameId: ConfigAPI.gameSakuLC,
      Stage: milestoneId,
      Competency: competenceId,
      Status: 0,
      DateCreated: new Date().getTime(),
      Score: -(gemHint || 0),
      HighestScore: 0,
      PlayedAt: new Date().getTime(),
      Name: `Sử dụng gợi ý - SAKULC_${milestoneId}`,
    };

    console.log('Tạo bản ghi mới:', game);
    const result = await gamecustomerController.add([game]);
    if (result.code !== 200) {
      return false;
    }
    setShowHintModel(true);
    return true;
  };

  return (
    <SafeAreaView style={{flex: 1}}>
      <ImageBackground
        style={{flex: 1}}
        source={require('./assets/background.png')}
        resizeMode="cover">
          <View
            style={{
              flex: 1,
              marginVertical: 16,
              marginHorizontal: 12,
              marginTop: 36,
            }}>
          {/* Header */}
          <HeadGame
            timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
            gameId={ConfigAPI.gameSakuLC}
            isShowSuggest={shouldShowHintButton}
            onUseHint={() => {
              setShowModelConfirm(true);
            }}
          />
          {isPauseGame ? (
            <GamePauseModal
              visible={isPauseGame}
              message={'Bạn đang tạm dừng trò chơi'}
              onContinue={() => {
                togglePauseGame();
              }}
            />
          ) : (
            <>
              <LineProgressBar
                progress={
                  (questionDone / totalQuestion) * 100
                }></LineProgressBar>
              <View
                style={{
                  width: '100%',
                  marginBottom: 16,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <Lives
                  totalLives={maxLives}
                  currentLives={currentLives}></Lives>
                <CountBadge
                  current={questionDone + 1}
                  total={totalQuestion}></CountBadge>
              </View>

              {/* Loading State */}
              {(loading || configLoading) && (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#112164" />
                  <Text style={styles.loadingText}>Đang tải câu hỏi...</Text>
                </View>
              )}

              {/* Error State */}
              {(error || configError) && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>
                    {error || configError || 'Không có câu hỏi cho game này'}
                  </Text>
                </View>
              )}

              {/* Game Content - Only show when data is loaded */}
              {initialized && configInitialized && currentQuestion && (
                <>
                  {/* Title question */}
                  <View style={styles.header}>
                    <View style={styles.instruction}>
                      <View style={styles.questionContainer}>
                        {currentQuestion.audioUrl && (
                          <TouchableOpacity
                            style={[
                              styles.audioButton,
                              isPauseGame && {opacity: 0.5},
                            ]}
                            onPress={isPauseGame ? undefined : playAudio}
                            disabled={isPauseGame}>
                            <Text style={styles.audioIcon}>
                              {isPlayingAudio ? '⏸️' : '🔊'}
                            </Text>
                          </TouchableOpacity>
                        )}
                        <Text style={styles.questionText}>
                          {currentQuestion.audioUrl ? 'Hãy nghe và sắp xếp các từ theo đúng thứ tự' :  currentQuestion.questionText}
                        </Text>
                      </View>
                    </View>
                  </View>

                  {/* Drop Zone */}
                  <View style={styles.dropZone}>
                    <View style={styles.dropZoneContent}>
                      {renderWordsInDropZone()}
                    </View>
                    {wordsInDropZone.length === 0 && (
                      <Text style={styles.dropZoneHint}>
                        Chạm vào các từ bên dưới để thêm vào đây
                      </Text>
                    )}

                    {/* Feedback Messages */}
                    {feedbackMessage && (
                      <View style={styles.feedbackContainer}>
                        <Text
                          style={[
                            styles.feedbackText,
                            feedbackType === 'success'
                              ? styles.successText
                              : styles.errorText,
                          ]}>
                          {feedbackMessage}
                        </Text>
                      </View>
                    )}
                  </View>
                </>
              )}
              {/* Check Answer Button */}
              <TouchableOpacity
                style={[
                  styles.checkButton,
                  isPauseGame && styles.disabledButton,
                ]}
                onPress={isPauseGame ? undefined : checkAnswer}
                disabled={isPauseGame}>
                <Text
                  style={[
                    styles.checkButtonText,
                    isPauseGame && styles.disabledButtonText,
                  ]}>
                  {isPauseGame ? 'Game đang tạm dừng' : 'Kiểm tra đáp án'}
                </Text>
              </TouchableOpacity>

              {/* Available Words - Only show when game is initialized */}
              {initialized && configInitialized && (
                <View style={styles.wordsContainer}>
                  {renderAvailableWords()}
                </View>
              )}
            </>
          )}

          {/* Control Buttons */}
          <View style={{position: 'absolute', bottom: 0, left: 0}}>
            <BottomGame
              resetGame={restartGame}
              backGame={() => {
                navigation.goBack();
              }}
              pauseGame={togglePauseGame}
              volumeGame={() => {}}
            />
          </View>
        </View>
      </ImageBackground>
      <View style={{zIndex: 1000}}>
        <ModelConfirm
          isShow={showModelConfirm}
          closeModal={() => setShowModelConfirm(false)}
          onConfirm={useHint}
          message={`Bạn sẽ bị trừ ${gemHint} Sakupi khi sử dụng trợ giúp này`}
        />
        <HintModel
          isShow={showHintModel}
          closeModal={() => setShowHintModel(false)}
          text={currentQuestion?.hint ?? ''}
        />
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={restartGame}
          message={messageGameOver}
          isTimeOut={false}
        />
        <WinnerModal
          visible={showWinnerModal}
          onClose={() => setShowWinnerModal(false)}
          restartGame={restartGame}
          currentLives={currentLives}
          competenceId={competenceId}
          gameId={ConfigAPI.gameSakuLC}
          currentMilestoneId={milestoneId}
        />
      </View>
    </SafeAreaView>
  );
};

export default StartSakuLC;
