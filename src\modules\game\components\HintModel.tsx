import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Image,
} from 'react-native';
import Sound from 'react-native-sound';
import ConfigAPI from '../../../Config/ConfigAPI';

// Utility function to check if text is an audio URL
const isAudioUrl = (text: string): boolean => {
  if (!text) return false;
  const audioExtensions = ['.mp3', '.wav', '.m4a', '.aac', '.ogg'];
  const lowerText = text.toLowerCase();
  return audioExtensions.some(ext => lowerText.includes(ext)) ||
         lowerText.includes('audio') ||
         lowerText.startsWith('http');
};

const HintModel = ({
  isShow,
  closeModal,
  text,
}: {
  isShow: boolean;
  closeModal: () => void;
  text: string;
}) => {
  const [audioPlayer, setAudioPlayer] = useState<Sound | null>(null);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [isAudioHint, setIsAudioHint] = useState(false);
  const audioRef = useRef<Sound | null>(null);

  // Check if hint is audio when modal opens
  useEffect(() => {
    if (isShow && text) {
      const isAudio = isAudioUrl(text);
      setIsAudioHint(isAudio);

      if (isAudio) {
        setupAudio(text);
      }
    }

    // Cleanup when modal closes
    if (!isShow) {
      cleanupAudio();
    }
  }, [isShow, text]);

  // Cleanup audio when component unmounts
  useEffect(() => {
    return () => {
      cleanupAudio();
    };
  }, []);

  const setupAudio = (audioUrl: string) => {
    console.log('===== SETTING UP HINT AUDIO =====');
    console.log('Audio URL:', audioUrl);

    // Clean up previous audio
    cleanupAudio();

    // Get valid audio URL
    const validUrl = ConfigAPI.getValidLink(audioUrl);

    // Enable playback in silence mode (iOS)
    Sound.setCategory('Playback');

    // Create new audio instance
    const sound = new Sound(validUrl, '', (error) => {
      if (error) {
        console.error('Failed to load hint audio:', error);
        setIsAudioHint(false); // Fallback to text display
        return;
      }
      console.log('Hint audio loaded successfully');
      setAudioPlayer(sound);
      audioRef.current = sound;
    });
  };

  const playAudio = () => {
    console.log('===== PLAY HINT AUDIO CALLED =====');

    if (!audioPlayer) {
      console.log('No audio player available');
      return;
    }

    if (isPlayingAudio) {
      // Stop audio if currently playing
      audioPlayer.stop(() => {
        console.log('Hint audio stopped');
        setIsPlayingAudio(false);
      });
    } else {
      // Play audio
      setIsPlayingAudio(true);
      audioPlayer.play((success) => {
        console.log('Hint audio play finished, success:', success);
        setIsPlayingAudio(false);
      });
    }
  };

  const cleanupAudio = () => {
    if (audioRef.current) {
      audioRef.current.stop();
      audioRef.current.release();
      audioRef.current = null;
    }
    setAudioPlayer(null);
    setIsPlayingAudio(false);
  };

  const handleCloseModal = () => {
    cleanupAudio();
    closeModal();
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={isShow}
        statusBarTranslucent={true}
        onRequestClose={handleCloseModal}>
        <View style={styles.modalOverlay}>
          {/* Close button */}
          <View
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 12,
              backgroundColor: 'white',
              borderRadius: 20,
            }}>
            <TouchableOpacity onPress={handleCloseModal}>
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            {/* Icon */}
            <View style={styles.iconContainer}>
              <Image source={require('../assets/magic_wand_gold.png')} />
            </View>

            {/* Content - Audio or Text */}
            {isAudioHint ? (
              <View style={styles.audioContainer}>
                <Text style={styles.audioLabel}>Gợi ý âm thanh</Text>
                <TouchableOpacity
                  style={[styles.audioButton, isPlayingAudio && styles.audioButtonPlaying]}
                  onPress={playAudio}
                  disabled={!audioPlayer}>
                  <Text style={styles.audioButtonText}>
                    {isPlayingAudio ? '⏸️ Dừng' : '🔊 Phát'}
                  </Text>
                </TouchableOpacity>
                {!audioPlayer && (
                  <Text style={styles.audioError}>Đang tải audio...</Text>
                )}
              </View>
            ) : (
              <Text style={styles.modalTitle}>{text}</Text>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5DC',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    padding: 12,
    backgroundColor: 'white',
    width: '85%',
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    maxWidth: 350,
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 15,
    right: 20,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    color: '#666',
    fontWeight: 'bold',
    marginBottom: 4,
    paddingHorizontal: 8,
  },
  iconContainer: {
    marginBottom: 20,
    marginTop: 10,
  },
  iconBackground: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#FFD700',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  iconText: {
    fontSize: 25,
  },
  modalTitle: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    lineHeight: 24,
    fontWeight: 'bold',
  },
  audioContainer: {
    alignItems: 'center',
    paddingVertical: 10,
  },
  audioLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  audioButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    minWidth: 120,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  audioButtonPlaying: {
    backgroundColor: '#FF6B35',
  },
  audioButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  audioError: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    marginTop: 10,
    textAlign: 'center',
  },
});

export default HintModel;
