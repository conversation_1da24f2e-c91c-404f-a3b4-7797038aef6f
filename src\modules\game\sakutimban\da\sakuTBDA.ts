import {DataController} from '../../../../base/baseController';
import {getRandomElements} from '../../../../utils/arrayUtils';
import {
  GameQuizQuestionAPI,
  GetQuestionsResponse,
  ApiResponse,
  SakuTBError,
  GameConfig,
} from '../types/sakuTBTypes';
import {
  transformQuestions,
  validateTransformedQuestion,
} from '../utils/sakuTBUtils';

class SakuTBDA {
  private questionController: DataController;
  //GameAnswer
  private answerController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuestion');
    this.answerController = new DataController('GameAnswer');
  }

  async loadGameQuestions({
    gameId,
    stage,
    competenceId,
  }: {
    gameId: string;
    stage: number;
    competenceId: string;
  }) {
    console.log(
      `[Redux] Loading questions for GameId: ${gameId}, Stage: ${stage}`,
    );
    const sakuTBDA = new SakuTBDA();
    const rawQuestions = await sakuTBDA.getQuestionsByGameAndStage(
      gameId,
      stage,
      competenceId,
    );
    
    const transformedQuestions = transformQuestions(rawQuestions);

    // Validate transformed questions
    const validQuestions = transformedQuestions.filter(
      validateTransformedQuestion,
    );

    if (validQuestions.length === 0)
      throw new Error('No valid questions after transformation');

    return {
      rawQuestions,
      transformedQuestions: validQuestions,
      isFromAPI: true,
    };
  }

  getRandomItemsBySort(arr: any, maxSort: number) {
    // Lấy ngẫu nhiên 5 số từ 1 đến maxSort
    const randomNumbers = [];
    const usedNumbers = new Set();

    while (randomNumbers.length < 5 && usedNumbers.size < maxSort) {
      const randomNum = Math.floor(Math.random() * maxSort) + 1;
      if (!usedNumbers.has(randomNum)) {
        randomNumbers.push(randomNum);
        usedNumbers.add(randomNum);
      }
    }

    // Lấy tất cả item có Sort bằng với các số đã chọn
    const result: any[] = [];

    randomNumbers.forEach(num => {
      const matchingItems = arr.filter((item: any) => item.Sort === num);
      result.push(...matchingItems);
    });
    

    return result;
  }

  getRandomAnswer(data: any[], questionId: string) {
    const answers = data
      .filter((answer: any) => answer.GameQuestionId === questionId)
      .sort((a, b) => a.Sort - b.Sort);
    if (answers.length === 0) return [];
    const maxSort = answers[answers.length - 1].Sort;
    
    const randomItems = this.getRandomItemsBySort(answers, maxSort);
    return randomItems;
  }

  /**
   * Random chọn số cặp đáp án từ danh sách answers
   * @param answers Danh sách tất cả answers của câu hỏi
   * @param pairCount Số cặp muốn lấy (mặc định 5)
   * @returns Danh sách answers đã được random
   */
  getRandomPairs(answers: any[], pairCount: number = 5) {
    if (!answers || answers.length === 0) {
      console.log(`[SakuTBDA] No answers provided for random pairs selection`);
      return [];
    }

    console.log(`[SakuTBDA] Input: ${answers.length} answers, requesting ${pairCount} pairs`);

    // Nhóm answers theo Sort (mỗi Sort là 1 cặp)
    const pairMap = new Map();
    answers.forEach(answer => {
      const sortKey = answer.Sort;
      if (!pairMap.has(sortKey)) {
        pairMap.set(sortKey, []);
      }
      pairMap.get(sortKey).push(answer);
    });

    // Lấy danh sách các Sort keys (các cặp)
    const sortKeys = Array.from(pairMap.keys()).sort((a, b) => a - b);

    console.log(`[SakuTBDA] Available pairs by Sort:`, sortKeys);

    // Random chọn pairCount cặp
    const selectedSorts = getRandomElements(sortKeys, Math.min(pairCount, sortKeys.length));

    // Lấy tất cả answers của các cặp đã chọn
    const selectedAnswers: any[] = [];
    selectedSorts.forEach(sortKey => {
      const pairAnswers = pairMap.get(sortKey);
      selectedAnswers.push(...pairAnswers);
    });

    console.log(`[SakuTBDA] Selected pairs:`, selectedSorts, `Total answers: ${selectedAnswers.length}`);

    return selectedAnswers;
  }

  /**
   * Lấy danh sách câu hỏi từ bảng GameQuizQuestion
   * @param gameId ID của game SakuTB
   * @param stage Stage hiện tại (mặc định = 1)
   * @returns Promise<GameQuizQuestionAPI[]>
   */
  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number = 1,
    competenceId: string,
  ): Promise<GameQuizQuestionAPI[]> {
    try {
      const response: GetQuestionsResponse =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}]`,
          sortby: {BY: 'Level', DIRECTION: 'ASC'}
        });
        
      if (response.code === 200) {
        // Lấy tất cả câu hỏi theo thứ tự (không random) - chỉ random answers
        // Lấy nhiều câu hỏi hơn từ mỗi level để tạo gameplay dài hơn
        const questionsLv1 = getRandomElements(
          response.data.filter((question: any) => question.Level == 1 || question.Level == '1' ),
          3, // Tăng từ 1 lên 3 câu
        );
        const questionsLv2 = getRandomElements(
          response.data.filter((question: any) => question.Level === 2 || question.Level == '2'),
          3, // Tăng từ 1 lên 3 câu
        );
        const questionsLv3 = getRandomElements(
          response.data.filter((question: any) => question.Level === 3 || question.Level == '3'),
          3, // Tăng từ 1 lên 3 câu
        );
        var questions = [...questionsLv1, ...questionsLv2, ...questionsLv3];
        if(questions.length === 0) {
          questions = response.data ?? [];
        }

        console.log(`[SakuTBDA] Loaded ${questions.length} questions in order (no random):`, {
          level1: questionsLv1.length,
          level2: questionsLv2.length,
          level3: questionsLv3.length,
          total: questions.length
        });
        //lấy danh sách đáp an
        const answerResponse = await this.answerController.getListSimple({
          query: `@GameQuestionId: {${questions
            .map((q: any) => q.Id)
            .join(' | ')}}`,
        });

        if (
          answerResponse &&
          answerResponse.data &&
          answerResponse.data.length > 0
        ) {
          // Map các câu trả lời vào câu hỏi và random 5 cặp
          questions.forEach((question: any) => {
            const allAnswers = answerResponse.data.filter(
              (answer: any) => answer.GameQuestionId === question.Id,
            );

            // Random chọn 5 cặp từ tất cả cặp có sẵn
            question.Answers = this.getRandomPairs(allAnswers, 5);
          });
        }
        // Validate questions
        const validQuestions = this.validateQuestions(questions);
        
        return validQuestions;
      } else {
        throw new Error(
          `API Error: ${response.message} (Code: ${response.code})`,
        );
      }
    } catch (error) {
      console.error('[SakuTBDA] Error fetching questions:', error);
      throw this.createError(
        'API_ERROR',
        'Failed to fetch questions from API',
        error,
      );
    }
  }

  /**
   * Lấy câu hỏi theo ID cụ thể
   * @param questionId ID của câu hỏi
   * @returns Promise<GameQuizQuestionAPI | null>
   */
  async getQuestionById(
    questionId: string,
  ): Promise<GameQuizQuestionAPI | null> {
    try {
      console.log(`[SakuTBDA] Fetching question by ID: ${questionId}`);

      const response = await this.questionController.getById(questionId);

      if (response.code === 200 && response.data) {
        return response.data as GameQuizQuestionAPI;
      }

      return null;
    } catch (error) {
      console.error('[SakuTBDA] Error fetching question by ID:', error);
      throw this.createError(
        'API_ERROR',
        'Failed to fetch question by ID',
        error,
      );
    }
  }

  /**
   * Lấy danh sách tất cả stages có sẵn cho game
   * @param gameId ID của game
   * @returns Promise<number[]>
   */
  async getAvailableStages(gameId: string): Promise<number[]> {
    try {
      console.log(`[SakuTBDA] Fetching available stages for GameId: ${gameId}`);

      const response: ApiResponse<GameQuizQuestionAPI> =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} AND @IsActive: true`,
          sortby: {BY: 'Stage', DIRECTION: 'ASC'},
        });

      if (response.code === 200) {
        const questions = response.data || [];
        // Lấy danh sách stages unique
        const stages = [...new Set(questions.map(q => q.Stage))].sort(
          (a, b) => a - b,
        );
        console.log(`[SakuTBDA] Available stages:`, stages);
        return stages;
      }

      return [1]; // Default stage
    } catch (error) {
      console.error('[SakuTBDA] Error fetching stages:', error);
      return [1]; // Fallback to stage 1
    }
  }

  /**
   * Validate questions data (hỗ trợ cả logic cũ và mới)
   * @param questions Raw questions from API
   * @returns Valid questions
   */
  private validateQuestions(
    questions: GameQuizQuestionAPI[],
  ): GameQuizQuestionAPI[] {
    return questions.filter(question => {
      try {
        
        // Check required fields
        if (!question.Id || !question.Name) {
          console.warn(
            `[SakuTBDA] Question ${question.Id} missing required fields`,
          );
          return false;
        }
        // Kiểm tra xem có Answers (logic mới) hay Options (logic cũ)
        const hasAnswers =
          (question as any).Answers && Array.isArray((question as any).Answers);

        if (hasAnswers) {
          // Validate logic mới với GameAnswer
          const answers = (question as any).Answers;

          if (answers.length === 0) {
            console.warn(`[SakuTBDA] Question ${question.Id} has no answers`);
            return false;
          }

          // Kiểm tra có left và right items
          const leftItems = answers.filter((a: any) => a.IsResult === true);
          const rightItems = answers.filter((a: any) => a.IsResult !== true);

          if (leftItems.length === 0 || rightItems.length === 0) {
            console.warn(
              `[SakuTBDA] Question ${question.Id} missing left or right items`,
            );
            return false;
          }

          // Kiểm tra Sort field
          const hasValidSort = answers.every(
            (a: any) => typeof a.Sort === 'number' && a.Sort > 0,
          );

          if (!hasValidSort) {
            console.warn(
              `[SakuTBDA] Question ${question.Id} has invalid Sort fields`,
            );
            return false;
          }

          return true;
        } else {
          console.warn(
            `[SakuTBDA] Question ${question.Id} missing both Answers and Options/CorrectAnswerIndex`,
          );
          return false;
        }
      } catch (error) {
        console.warn(
          `[SakuTBDA] Question ${question.Id} validation failed:`,
          error,
        );
        return false;
      }
    });
  }

  /**
   * Create standardized error
   * @param type Error type
   * @param message Error message
   * @param details Additional details
   * @returns SakuTBError
   */
  private createError(
    type: SakuTBError['type'],
    message: string,
    details?: any,
  ): SakuTBError {
    return {
      type,
      message,
      details,
    };
  }
  async loadGameConfig(gameId: string): Promise<any> {
    try {
      const controller = new DataController('GameConfig');
      const response = await controller.getListSimple({
        query: `@GameId: {${gameId}}`,
      });
      if (
        response.code !== 200 ||
        !response.data ||
        response.data.length === 0
      ) {
        throw new Error(
          'No game config found or API returned unsuccessful response',
        );
      }
      
      const configData = response.data[0];
      return {
        scorePerLife: configData.Score,
        maxLives: configData.LifeCount,
        timeLimit: configData.Time,
        bonusScore: configData.Bonus,
        isActive: true,
      };
    } catch (error) {
      console.error('[GameConfigDA] Error loading game config:', error);
      throw error;
    }
  }
  calculateScore(
    config: GameConfig,
    livesRemaining: number,
    totalLives: number,
  ): number {
    const baseScore = livesRemaining * config.scorePerLife;

    // Bonus nếu không mất mạng nào
    const bonus = livesRemaining === totalLives ? config.bonusScore : 0;

    const finalScore = baseScore + bonus;

    return finalScore;
  }
}

export default new SakuTBDA();
