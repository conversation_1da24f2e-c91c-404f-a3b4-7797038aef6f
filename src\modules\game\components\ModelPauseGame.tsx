import React, {useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  Dimensions,
  ImageBackground,
} from 'react-native';
import {useGameHook} from '../../../redux/hook/gameHook';

const {width, height} = Dimensions.get('window');

interface GamePauseModalProps {
  visible: boolean;
  message: string;
  onContinue: () => void;
}

const GamePauseModal = ({
  visible,
  message,
  onContinue,
}: GamePauseModalProps) => {
  const gameHook = useGameHook();
  useEffect(() => {
    if (visible) {
      gameHook.pauseGame();
    } else {
      gameHook.continueGame();
    }
  }, [visible]);
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      statusBarTranslucent={true}
      onRequestClose={onContinue}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* <PERSON><PERSON><PERSON>nh con chim buồn */}
          <View style={styles.birdContainer}>
            <Image source={require('../assets/pause_bird.png')} />
            <ImageBackground
              source={require('../ailatrieuphu/assets/speech-bubble.png')}
              style={styles.speechBubble}>
              <Text style={styles.messageText}>{message}</Text>
            </ImageBackground>
          </View>
          {/* Nút Next */}
          <TouchableOpacity style={styles.restartButton} onPress={onContinue}>
            <Image
              source={require('../assets/Button_start.png')}
              style={styles.restartButton}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.69)',
    justifyContent: 'center',
  },
  modalContent: {
    width: width * 0.93,
    height: height * 0.76,
    padding: 20,
    marginBottom: 20,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },

  birdContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginTop: 180,
    marginRight: 120,
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
    height: '100%',
  },
  speechBubble: {
    position: 'absolute',
    top: -130,
    right: -70,
    borderRadius: 20,
    padding: 15,
    width: 210,
    height: 159,
  },
  messageText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    fontWeight: 'bold',
    lineHeight: 20,
    maxWidth: 180,
    fontFamily: 'BagelFatOne-Regular',
    marginTop: 35,
  },
  restartButton: {
    width: 230,
    height: 90,
  },
});

export default GamePauseModal;
