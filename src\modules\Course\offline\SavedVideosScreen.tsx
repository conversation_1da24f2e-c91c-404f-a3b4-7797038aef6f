import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  AppButton,
  Winicon,
  showSnackbar,
  ComponentStatus,
  ListTile,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import OfflineVideoStorage, {
  VideoGroupedByLesson,
} from '../../../utils/OfflineVideoStorage';
import VideoDownloadManager, {
  SavedVideo,
} from '../../../utils/VideoDownloadManager';
import {useNavigation, useRoute} from '@react-navigation/native';
import {navigateBack, navigate} from '../../../router/router';
import ScreenHeader from '../../../Screen/Layout/header';
import EmptyPage from '../../../Screen/emptyPage';

const SavedVideosScreen: React.FC = () => {
  const {t} = useTranslation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [videoGroups, setVideoGroups] = useState<VideoGroupedByLesson[]>([]);
  const [storageInfo, setStorageInfo] = useState({
    totalVideos: 0,
    totalSize: 0,
    totalSizeFormatted: '0 B',
  });
  const [expandedLessons, setExpandedLessons] = useState<Set<string>>(
    new Set(),
  );

  const offlineStorage = OfflineVideoStorage.getInstance();
  const downloadManager = VideoDownloadManager.getInstance();
  const navigation = useNavigation<any>();

  //get route with courseId
  const route = useRoute<any>();
  const courseId = route.params?.courseId;

  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Get videos grouped by lesson
      const groups = await offlineStorage.getVideosGroupedByLesson();

      if (courseId) {
        const filteredGroups = groups.filter(
          group => group.courseId === courseId,
        );
        setVideoGroups(filteredGroups);
      } else {
        setVideoGroups(groups);
      }
      // Get storage info
      const info = await offlineStorage.getStorageInfo();

      setStorageInfo({
        totalVideos: info.totalVideos,
        totalSize: info.totalSize,
        totalSizeFormatted: info.totalSizeFormatted,
      });

      // Expand all lessons by default if there are few
      if (groups.length <= 3) {
        const lessonIds = new Set(groups.map(g => g.lessonId));
        setExpandedLessons(lessonIds);
      }
    } catch (error) {
      console.error('Error loading saved videos:', error);
      showSnackbar({
        message: t('video.loadVideosFailed'),
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadData();
  }, [loadData]);

  const toggleLessonExpand = (lessonId: string) => {
    setExpandedLessons(prev => {
      const newSet = new Set(prev);
      if (newSet.has(lessonId)) {
        newSet.delete(lessonId);
      } else {
        newSet.add(lessonId);
      }
      return newSet;
    });
  };

  const handlePlayVideo = (video: SavedVideo) => {
    navigation.navigate('OfflineVideoPlayer', {video});
  };

  const handleDeleteVideo = (video: SavedVideo) => {
    Alert.alert(
      t('video.deleteVideo'),
      t('video.deleteVideoConfirm', {videoName: video.videoName}),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await offlineStorage.deleteVideo(video.id);
              if (success) {
                // Also remove from download manager
                await downloadManager.deleteVideo(video.id);

                showSnackbar({
                  message: t('video.deleteVideoSuccess'),
                  status: ComponentStatus.SUCCSESS,
                });
                loadData();
              } else {
                showSnackbar({
                  message: t('video.deleteVideoFailed'),
                  status: ComponentStatus.ERROR,
                });
              }
            } catch (error) {
              console.error('Error deleting video:', error);
              showSnackbar({
                message: t('video.deleteVideoError'),
                status: ComponentStatus.ERROR,
              });
            }
          },
        },
      ],
    );
  };

  const handleDeleteLesson = (lessonId: string, lessonName: string) => {
    Alert.alert(
      t('video.deleteAllVideos'),
      t('video.deleteAllVideosConfirm', {lessonName}),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.deleteAll'),
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await offlineStorage.deleteVideosByLesson(
                lessonId,
              );
              if (success) {
                // Clean up download manager cache
                await downloadManager.cleanupInvalidRecords();

                showSnackbar({
                  message: t('video.deleteAllVideosSuccess'),
                  status: ComponentStatus.SUCCSESS,
                });
                loadData();
              } else {
                showSnackbar({
                  message: t('video.deleteVideoFailed'),
                  status: ComponentStatus.ERROR,
                });
              }
            } catch (error) {
              console.error('Error deleting lesson videos:', error);
              showSnackbar({
                message: t('video.deleteVideoError'),
                status: ComponentStatus.ERROR,
              });
            }
          },
        },
      ],
    );
  };

  const handleClearAllVideos = () => {
    Alert.alert(
      t('video.deleteAllSavedVideos'),
      t('video.deleteAllSavedVideosConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.deleteAll'),
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await offlineStorage.clearAllVideos();
              if (success) {
                // Clear download manager cache
                downloadManager.clearDownloadCache();

                showSnackbar({
                  message: t('video.deleteAllVideosSuccess'),
                  status: ComponentStatus.SUCCSESS,
                });
                loadData();
              } else {
                showSnackbar({
                  message: t('video.deleteVideoFailed'),
                  status: ComponentStatus.ERROR,
                });
              }
            } catch (error) {
              console.error('Error clearing all videos:', error);
              showSnackbar({
                message: t('video.deleteVideoError'),
                status: ComponentStatus.ERROR,
              });
            }
          },
        },
      ],
    );
  };

  const renderLessonItem = ({item}: {item: VideoGroupedByLesson}) => {
    const isExpanded = expandedLessons.has(item.lessonId);

    return (
      <View style={styles.lessonContainer}>
        <TouchableOpacity
          style={styles.lessonHeader}
          onPress={() => toggleLessonExpand(item.lessonId)}>
          <View style={styles.lessonTitleContainer}>
            <Winicon
              src="fill/business/books"
              size={24}
              color={ColorThemes.light.Primary_Color_Main}
            />
            <View style={styles.lessonInfo}>
              <Text style={styles.lessonTitle}>{item.lessonName}</Text>
              <Text style={styles.lessonSubtitle}>
                {item.videos.length} video • {item.totalSizeFormatted}
              </Text>
            </View>
          </View>

          <View style={styles.lessonActions}>
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() =>
                handleDeleteLesson(item.lessonId, item.lessonName)
              }>
              <Winicon
                src="outline/layout/trash-2"
                size={20}
                color={ColorThemes.light.Error_Color_Main}
              />
            </TouchableOpacity>

            <Winicon
              src={
                isExpanded
                  ? 'fill/arrows/down-arrow'
                  : 'fill/arrows/right-arrow'
              }
              size={20}
              color={ColorThemes.light.Neutral_Text_Color_Subtitle}
            />
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.videoList}>
            {item.videos.map(video => renderVideoItem(video))}
          </View>
        )}
      </View>
    );
  };

  const renderVideoItem = (video: SavedVideo) => {
    return (
      <ListTile
        key={video.id}
        style={styles.videoItem}
        onPress={() => handlePlayVideo(video)}
        leading={
          <View style={styles.videoThumbnail}>
            <Winicon
              src="color/multimedia/video-gallery"
              size={24}
              color={ColorThemes.light.white}
            />
          </View>
        }
        title={video.videoName}
        titleStyle={styles.videoTitle}
        subtitle={`${new Date(video.downloadDate).toLocaleDateString()}`}
        subTitleStyle={styles.videoSubtitle}
        trailing={
          <TouchableOpacity
            style={styles.videoDeleteButton}
            onPress={() => handleDeleteVideo(video)}>
            <Winicon
              src="outline/layout/trash-2"
              size={18}
              color={ColorThemes.light.Error_Color_Main}
            />
          </TouchableOpacity>
        }
      />
    );
  };

  const renderEmptyState = () => {
    if (loading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator
            size="large"
            color={ColorThemes.light.Primary_Color_Main}
          />
          <Text style={styles.emptyText}>{t('video.loadingVideos')}</Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <EmptyPage
          title={t('video.noSavedVideos')}
          subtitle={t('video.noSavedVideosSubtitle')}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <SafeAreaView edges={['top']} />
      <ScreenHeader
        title={t('video.savedVideos')}
        backIcon={<Winicon src="outline/arrows/left-arrow" size={20} />}
        onBack={() => {
          navigateBack();
        }}
      />

      {/* Storage info */}
      {!loading && videoGroups.length > 0 && !courseId && (
        <View style={styles.storageInfoContainer}>
          <Text style={styles.storageInfoText}>
            {storageInfo.totalVideos} video • {storageInfo.totalSizeFormatted}
          </Text>
          <TouchableOpacity
            style={styles.clearAllButton}
            onPress={handleClearAllVideos}>
            <Text style={styles.clearAllText}>{t('common.deleteAll')}</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Video list */}
      <FlatList
        data={videoGroups}
        renderItem={renderLessonItem}
        keyExtractor={item => item.lessonId}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[ColorThemes.light.Primary_Color_Main]}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  headerTitleContainer: {
    flex: 1,
    marginLeft: 8,
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  storageInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  storageInfoText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  clearAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    backgroundColor: ColorThemes.light.Error_Color_Background,
  },
  clearAllText: {
    ...TypoSkin.buttonText4,
    color: ColorThemes.light.Error_Color_Main,
  },
  listContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  lessonContainer: {
    marginHorizontal: 16,
    marginTop: 16,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  lessonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  lessonTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  lessonInfo: {
    marginLeft: 12,
    flex: 1,
  },
  lessonTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  lessonSubtitle: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    fontSize: 12,
  },
  lessonActions: {
    gap: 6,
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButton: {
    marginRight: 4,
  },
  videoList: {
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  videoItem: {
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  videoThumbnail: {
    width: 48,
    height: 48,
    borderRadius: 4,
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoTitle: {
    ...TypoSkin.body1,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  videoSubtitle: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    fontSize: 12,
  },
  videoDeleteButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    textAlign: 'center',
  },
});

export default SavedVideosScreen;
