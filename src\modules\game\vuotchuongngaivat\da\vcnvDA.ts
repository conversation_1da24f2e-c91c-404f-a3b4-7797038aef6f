import {DataController} from '../../../../base/baseController';
import {BaseDA} from '../../../../base/BaseDA';
import {
  VCNVGameConfigAPI,
  VCNVGameQuestionAPI,
  VCNVGameAnswerAPI,
  VCNVGameConfig,
  VCNVQuestion,
  VCNVWord,
  VCNVCrosswordData,
  VCNVGridCell,
  ApiResponse,
  JapaneseSplitResponse,
  GetVCNVQuestionsResponse,
  GetVCNVAnswersResponse,
  GetVCNVConfigResponse,
} from '../types/vcnvTypes';

export class VCNVDA {
  private questionController: DataController;
  private answerController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuestion');
    this.answerController = new DataController('GameAnswer');
  }

  /**
   * L<PERSON>y c<PERSON>u hình game từ bảng GameConfig
   * @param gameId ID của game VCNV
   * @returns Promise<VCNVGameConfig>
   */
  static async getGameConfig(gameId: string): Promise<VCNVGameConfig> {
    try {
      console.log(`[VCNVDA] Loading game config for GameId: ${gameId}`);

      const controller = new DataController('GameConfig');
      const response: GetVCNVConfigResponse = await controller.getListSimple({
        query: `@GameId: {${gameId}}`,
      });

      if (
        response.code !== 200 ||
        !response.data ||
        response.data.length === 0
      ) {
        throw new Error(
          'No game config found or API returned unsuccessful response',
        );
      }
      const configData = response.data[0];
      return {
        gameId: configData.GameId,
        scorePerLife: configData.Score,
        maxLives: configData.LifeCount,
        timeLimit: configData.Time,
        bonusScore: configData.Bonus,
        isActive: configData.IsActive,
        gemHint: configData.ScoreHint,
      };
    } catch (error) {
      console.error('[VCNVDA] Error loading game config:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách câu hỏi từ bảng GameQuestion
   * @param gameId ID của game
   * @param stage Stage của game
   * @param competenceId ID competence
   * @returns Promise<VCNVGameQuestionAPI[]>
   */
  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number,
    competenceId: string,
  ): Promise<VCNVGameQuestionAPI[]> {
    try {
      console.log(
        `[VCNVDA] Loading questions for GameId: ${gameId}, Stage: ${stage}, CompetenceId: ${competenceId}`,
      );

      // 1. Lấy ra 1 câu hỏi có Collection lớn nhất
      const questionMaxCollection = await this.questionController.getListSimple(
        {
          page: 1,
          size: 1,
          query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}]`,
          sortby: {BY: 'Collection', DIRECTION: 'DESC'},
        },
      );

      if (
        questionMaxCollection.code !== 200 ||
        !questionMaxCollection.data ||
        questionMaxCollection.data.length === 0
      ) {
        throw new Error('No questions found to determine max Collection');
      }

      const maxCollection = questionMaxCollection.data[0].Collection;
      console.log(`[VCNVDA] Max Collection found: ${maxCollection}`);
      // 2. Chọn random 1 số từ 1 đến Collection lớn nhất
      const randomCollection = Math.floor(Math.random() * maxCollection) + 1;
      console.log(`[VCNVDA] Random Collection selected: ${randomCollection}`);

      const response: GetVCNVQuestionsResponse =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}] @Collection: [${randomCollection}]`,
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        });

      if (response.code !== 200) {
        throw new Error(`API returned error code: ${response.code}`);
      }

      if (!response.data || response.data.length === 0) {
        throw new Error('No questions found for the specified criteria');
      }

      // Validate có đủ ít nhất 2 câu (1 câu hỏi, 1 câu keyword)
      if (response.data.length < 2) {
        throw new Error(
          `Expected at least 2 questions, but got ${response.data.length}`,
        );
      }

      return response.data;
    } catch (error) {
      console.error('[VCNVDA] Error loading questions:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách đáp án từ bảng GameAnswer
   * @param questionIds Danh sách ID câu hỏi
   * @returns Promise<VCNVGameAnswerAPI[]>
   */
  async getAnswersByQuestionIds(
    questionIds: string[],
  ): Promise<VCNVGameAnswerAPI[]> {
    try {
      console.log(
        `[VCNVDA] Loading answers for questions: ${questionIds.join(', ')}`,
      );

      const response: GetVCNVAnswersResponse =
        await this.answerController.getListSimple({
          query: `@GameQuestionId: {${questionIds.join(' | ')}}`,
        });

      if (response.code !== 200) {
        throw new Error(`API returned error code: ${response.code}`);
      }

      if (!response.data || response.data.length === 0) {
        throw new Error('No answers found for the specified questions');
      }

      return response.data;
    } catch (error) {
      console.error('[VCNVDA] Error loading answers:', error);
      throw error;
    }
  }

  /**
   * Transform API data thành game data
   * @param questions Danh sách câu hỏi từ API
   * @param answers Danh sách đáp án từ API
   * @returns Promise<VCNVQuestion[]>
   */
  async transformToGameData(
    questions: VCNVGameQuestionAPI[],
    answers: VCNVGameAnswerAPI[],
  ): Promise<VCNVQuestion[]> {
    try {
      console.log('[VCNVDA] Transforming API data to game data');
      const transformedQuestions: VCNVQuestion[] = [];

      questions.forEach((question, index) => {
        // Tìm answer tương ứng với question
        const questionAnswer = answers.find(
          a => a.GameQuestionId === question.Id,
        );
        if (!questionAnswer) {
          throw new Error(`No answer found for question ${question.Id}`);
        }

        let words: VCNVWord[] = [];
        let keywordAnswer: string | undefined;

        if (index === questions.length - 1) {
          // Câu hỏi từ khóa dọc - không cần split, chỉ lưu đáp án
          keywordAnswer = questionAnswer.Name;
        } else {
          console.log('questionAnswer.Name', questionAnswer.Name);
          // Câu hỏi hàng ngang - split thành từ
          const splitResult = questionAnswer.Name.split(/[&＆]/); // Split theo ký tự & hoặc ＆

          words = splitResult.map((token: any, index: number) => ({
            id: `${question.Id}_${index}`,
            text: token,
            correctPosition: index + 1,
            isKey: index + 1 === questionAnswer.Sort, // Position bắt đầu từ 1
          }));
        }

        transformedQuestions.push({
          id: question.Id,
          questionText: question.Name,
          sort: question.Sort,
          words,
          keywordAnswer,
          stage: question.Stage,
          competenceId: question.Purpose,
          hint: question.Suggest,
          audioUrl: question.Audio, // Thêm audio URL
        });
      });

      return transformedQuestions;
    } catch (error) {
      console.error('[VCNVDA] Error transforming data:', error);
      throw error;
    }
  }

  /**
   * Tạo crossword data từ transformed questions
   * @param questions Danh sách câu hỏi đã transform
   * @returns VCNVCrosswordData
   */
  static createCrosswordData(questions: VCNVQuestion[]): VCNVCrosswordData {
    try {
      console.log('[VCNVDA] Creating crossword data');

      // Tách câu hỏi hàng ngang và từ khóa dọc
      const keywordQuestion = questions[questions.length - 1];
      const horizontalQuestions = questions.slice(0, questions.length - 1);

      if (!keywordQuestion) {
        throw new Error('Keyword question not found');
      }

      // Tạo grid layout 8x9 (8 hàng, 9 cột)
      const gridLayout: VCNVGridCell[][] = [];

      horizontalQuestions.forEach((question, index) => {
        const rowCells: VCNVGridCell[] = [];

        for (let col = 0; col < 9; col++) {
          const cell: VCNVGridCell = {
            row: index,
            col,
            isKeyColumn: false,
            isActive: false,
          };

          // Key column luôn là cột thứ 5 (index 4)
          const keyColumnIndex = 4;
          const keyWordIndex = question.words.findIndex(w => w.isKey);

          if (keyWordIndex !== -1 && col === keyColumnIndex) {
            cell.isKeyColumn = true;
          }

          // Tính toán vị trí bắt đầu của từ để key column ở giữa (cột 4)
          const wordStartCol = keyColumnIndex - keyWordIndex;
          const wordEndCol = wordStartCol + question.words.length - 1;

          // Xác định ô có được sử dụng không
          if (
            col >= wordStartCol &&
            col <= wordEndCol &&
            wordStartCol >= 0 &&
            wordEndCol < 9
          ) {
            cell.isActive = true;
            cell.questionId = question.id;
            cell.wordIndex = col - wordStartCol;
          }

          rowCells.push(cell);
        }

        gridLayout.push(rowCells);
      });

      return {
        horizontalQuestions,
        keywordQuestion,
        gridLayout,
      };
    } catch (error) {
      console.error('[VCNVDA] Error creating crossword data:', error);
      throw error;
    }
  }

  /**
   * Load complete game data
   * @param gameId ID của game
   * @param stage Stage hiện tại
   * @param competenceId ID competence
   * @returns Promise<VCNVCrosswordData>
   */
  async loadCompleteGameData(
    gameId: string,
    stage: number,
    competenceId: string,
  ): Promise<VCNVCrosswordData> {
    try {
      console.log(`[VCNVDA] Loading complete game data for GameId: ${gameId}`);

      // Load questions
      const questions = await this.getQuestionsByGameAndStage(
        gameId,
        stage,
        competenceId,
      );

      // Load answers
      const answers = await this.getAnswersByQuestionIds(
        questions.map(q => q.Id),
      );
      // Transform data
      const transformedQuestions = await this.transformToGameData(
        questions,
        answers,
      );

      // Create crossword data
      const crosswordData = VCNVDA.createCrosswordData(transformedQuestions);
      console.log('🚀 ~ VCNVDA ~ crosswordData:', crosswordData);

      return crosswordData;
    } catch (error) {
      console.error('[VCNVDA] Error loading complete game data:', error);
      throw error;
    }
  }
}
