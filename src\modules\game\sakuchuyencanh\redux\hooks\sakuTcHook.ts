import {useDispatch} from 'react-redux';
import {
  setData,
  startGame,
  nextQuestion,
  nextLevel,
  restartLevel,
  resetGame,
  applyGameConfig,
} from '../reducers/sakuTcReducer';

export const useSakuTcHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: ({stateName, value}: {stateName: string; value: any}) => {
      dispatch(setData({stateName, value}));
    },
    applyGameConfig: (config: any) => {
      dispatch(applyGameConfig(config));
    },
    startGame: () => {
      dispatch(startGame());
    },
    nextQuestion: () => {
      dispatch(nextQuestion());
    },
    nextLevel: () => {
      dispatch(nextLevel());
    },
    restartLevel: () => {
      dispatch(restartLevel());
    },
    resetGame: () => {
      dispatch(resetGame());
    },
  };

  return action;
};
