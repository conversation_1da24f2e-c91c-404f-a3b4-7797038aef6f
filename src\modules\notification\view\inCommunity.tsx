import {
  Di<PERSON><PERSON>,
  FlatList,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import NotificationCard from '../components/card/notification-card';
import {CardNotificationSkeleton} from '../components/card/notification-shimmer';
import {useDispatch} from 'react-redux';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {NotificationActions} from '../../../redux/reducers/notificationReducer';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import {DataController} from '../../../base/baseController';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';
import EmptyPage from '../../../Screen/emptyPage';
import {useSelectorNotificationState} from '../../../redux/hook/notificationHook';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';

export default function NotifCommunity() {
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  const noti = useSelectorNotificationState();
  const scrSize = Dimensions.get('screen');
  const [isRefreshing, setRefresh] = useState(false);
  const dispatch = useDispatch<any>();
  const [tab, setTab] = useState(0);

  const getData = () => {
    
    NotificationActions.getData(dispatch, {
      page: 1,
      status: tab === 0 ? undefined : 0,
    });
  };

  useEffect(() => {
    if (user) {
      getData();
    }
  }, [user, tab]);

  const onRefresh = async () => {
    setRefresh(true);
    if (user) getData();
    if (noti.onLoading === false) {
      setRefresh(false);
    } else {
      setTimeout(() => {
        setRefresh(false);
      }, 2000);
    }
  };

  return (
     <SafeAreaView
          style={{
            flex: 1,
            backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          }}>
            <ScreenHeader
                    onBack={() => {
                      navigation.pop();
                    }}
                    title={t('notification.title')}
                    action={
                      <TouchableOpacity
                        onPress={async () => {
                          const controller = new DataController('Notification');
                          if (user) {
                            const unReadNoti = await controller.getListSimple({
                              page: 1,
                              size: 1000,
                              query: `@CustomerId:{${user.Id}} @Status:[0 0]`,
                            });
                            if (unReadNoti.code === 200 && unReadNoti.data.length)
                              controller
                                .edit(unReadNoti.data.map((e: any) => ({...e, Status: 1})))
                                .then(res => {
                                  if (res.code === 200) {
                                    NotificationActions.setBadge(-unReadNoti.data.length,dispatch);
                                    getData();
                                  };
                                });
                            showSnackbar({
                              message: t('notification.markAllAsRead'),
                              status: ComponentStatus.INFOR,
                            });
                          }
                        }}
                        style={{paddingRight: 16}}>
                        <Text style={{color: ColorThemes.light.Neutral_Text_Color_Title}}>
                          {t('notification.readAll')}
                        </Text>
                      </TouchableOpacity>
                    }
                  />
            <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <View
        style={{
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          flex: 1,
          alignContent: 'center',
        }}>
        <View
          style={{
            marginTop: 8,
            height: 40,
            paddingVertical: 4,
            paddingHorizontal: 16,
            gap: 16,
            flexDirection: 'row',
          }}>
          <TouchableOpacity
            onPress={() => {
              setTab(0);
            }}
            style={{
              paddingVertical: 4,
              paddingHorizontal: 8,
              borderRadius: 16,
              backgroundColor:
                tab === 0
                  ? ColorThemes.light.Neutral_Background_Color_AbsoluteReverse
                  : ColorThemes.light.Neutral_Background_Color_Absolute,
              borderColor: ColorThemes.light.Neutral_Border_Color_Main,
            }}>
            <Text
              style={[
                TypoSkin.regular2,
                {
                  color:
                    tab === 0
                      ? ColorThemes.light.Neutral_Background_Color_Absolute
                      : ColorThemes.light
                          .Neutral_Background_Color_AbsoluteReverse,
                },
              ]}>
              {t('notification.all')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setTab(1);
            }}
            style={{
              paddingVertical: 4,
              paddingHorizontal: 8,
              borderRadius: 16,
              backgroundColor:
                tab === 1
                  ? ColorThemes.light.Neutral_Background_Color_AbsoluteReverse
                  : ColorThemes.light.Neutral_Background_Color_Absolute,
              borderColor: ColorThemes.light.Neutral_Border_Color_Main,
            }}>
            <Text
              style={[
                TypoSkin.regular2,
                {
                  color:
                    tab === 1
                      ? ColorThemes.light.Neutral_Background_Color_Absolute
                      : ColorThemes.light
                          .Neutral_Background_Color_AbsoluteReverse,
                },
              ]}>
              {t('notification.unread')}
            </Text>
          </TouchableOpacity>
          <View style={{flex: 1}} />
        </View>
        {noti.data.length === 0 && !noti.onLoading ? (
          <View style={{flex: 1}}>
            <EmptyPage title={t('notification.noNotifications')} />
          </View>
        ) : (
          <FlatList
            nestedScrollEnabled={true}
            refreshControl={
              <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
            }
            style={{flex: 1, height: '100%', alignContent: 'center'}}
            data={noti.data}
            renderItem={({item, index}) => {
              return <NotificationCard tab={tab} item={item} />;
            }}
            keyExtractor={(item, i) => `${i}`}
            ListEmptyComponent={() => {
              if (noti.onLoading)
                return Array.from({length: 5}).map((_, i) => (
                  <CardNotificationSkeleton key={i} />
                ));
            }}
          />
        )}
      </View>
    </View>
          </SafeAreaView>
    
  );
}
