/* eslint-disable react-native/no-inline-styles */
import React, {
  useRef,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useState,
} from 'react';
import {
  View,
  Text,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
} from 'react-native';
import {useImagePicker} from './hooks/useImagePicker';
import {extractVideoLinks} from './utils/textUtils';
import ImagePreview from './ImagePreview';
import Toolbar from './Toolbar';
import VideoModal from './VideoModal';
import SuggestionModal from './SuggestionModal';
import {useMentionHashtagSuggestion} from './hooks/useMentionHashtagSuggestion';
import {
  RichTextComposerProps,
  RichTextComposerRef,
  PostData,
  ImageItem,
} from './types';
import {styles} from './styles';
import {ColorThemes} from '../../../../assets/skin/colors';
import ConfigAPI from '../../../../Config/ConfigAPI';
import PickColorLine from './PickColorLine';
import EditorBackground from './EditorBackgound';
import {BackgroundData} from '../../../../redux/models/PostBackground';

const RichTextComposer = forwardRef<RichTextComposerRef, RichTextComposerProps>(
  (props, ref) => {
    const {
      onTextChange,
      onImagesChange,
      onDataChange,
      initialText = '',
      initialImages = [],
      initialHtml = '',
      initialImageIds = [],
      initialBackgroundData,
      maxImages = 10,
    } = props;

    const inputRef = useRef<TextInput>(null);
    const scrollViewRef = useRef<ScrollView>(null);
    const isSelectingSuggestionRef = useRef(false);
    const [isResetEditorBackground, setIsResetEditorBackground] =
      useState(false);
    const [editorLayout, setEditorLayout] = useState({height: 0, y: 0});
    const [backgroundData, setBackgroundData] = useState<BackgroundData>({
      Id: '0',
      Type: 999,
      Img: '',
      ColorMobile: '',
      TextColor: '',
    });
    const [showVideoModal, setShowVideoModal] = useState(false);
    const [text, setText] = useState(initialText || '');
    const [selection, setSelection] = useState({start: 0, end: 0});
    const [textInputLayout, setTextInputLayout] = useState({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    });

    // Hook cho mention/hashtag suggestion
    const {
      suggestionState,
      handleTextChange: handleSuggestionTextChange,
      handleSuggestionSelect,
      hideSuggestion,
    } = useMentionHashtagSuggestion();

    // Handler cho text input
    const handleTextChange = (newText: string) => {
      setText(newText);

      // Không xử lý suggestion nếu đang trong quá trình chọn suggestion
      if (!isSelectingSuggestionRef.current) {
        // Delay nhỏ để đảm bảo selection được cập nhật trước khi xử lý suggestion
        setTimeout(() => {
          handleSuggestionTextChange(newText, selection.start, textInputLayout);
        }, 10);
      }
    };

    const handleSelectionChange = (event: any) => {
      const newSelection = event.nativeEvent.selection;
      setSelection(newSelection);

      // Chỉ cập nhật suggestion khi cursor thực sự di chuyển và không phải do programmatic change
      if (newSelection.start === newSelection.end && !isSelectingSuggestionRef.current) {
        setTimeout(() => {
          handleSuggestionTextChange(text, newSelection.start, textInputLayout);
        }, 10);
      }
    };

    const handleTextInputLayout = (event: any) => {
      const {layout} = event.nativeEvent;
      setTextInputLayout(layout);
    };

    // Xử lý khi chọn suggestion
    const onSelectSuggestion = (item: any) => {
      // Đánh dấu đang trong quá trình chọn suggestion
      isSelectingSuggestionRef.current = true;

      const result = handleSuggestionSelect(item, text, selection.start);
      setText(result.newText);

      // Focus vào TextInput trước khi set selection
      inputRef.current?.focus();
      
      // Cập nhật cursor position với delay để đảm bảo text đã được update
      setTimeout(() => {
        setSelection({
          start: result.newCursorPosition,
          end: result.newCursorPosition,
        });

        // Reset flag sau khi hoàn thành
        setTimeout(() => {
          isSelectingSuggestionRef.current = false;
        }, 100);
      }, 300);
    };

    const {
      selectedImages,
      setSelectedImages,
      handlePickImages,
      handleRemoveImage,
      handleRemoveAllImages,
    } = useImagePicker(initialImages, initialImageIds, maxImages);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      getPostData: () => {
        const data: any = {
          text: text,
          segments: [], // Không còn sử dụng segments
          images: selectedImages,
          existingImageIds: selectedImages
            .filter(img => img.existingId)
            .map(img => img.existingId as string),
        };
        if (backgroundData.Type !== 999) data.backgroundData = backgroundData;
        return data;
      },
      clearContent: () => {
        setText('');
        setSelectedImages([]);
        setBackgroundData({
          Id: '0',
          Type: 999,
          Img: '',
          ColorMobile: '',
          TextColor: '',
        });
      },
      getContentWithMentions: () => {
        return text; // Trả về text thô, không có mention processing
      },
      getHashtags: () => {
        return ''; // Không còn hashtag processing
      },
      getVideoLinks: () => {
        return extractVideoLinks(text);
      },
      setContent: (
        newText: string,
        images: ImageItem[] = [],
        html?: string,
        imageIds?: string[],
      ) => {
        // Nếu có HTML, chuyển thành plain text
        if (html) {
          const plainText = html.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').trim();
          setText(plainText);
        } else {
          setText(newText);
        }

        if (images.length > 0) {
          setSelectedImages(images);
        } else if (imageIds && imageIds.length > 0) {
          setSelectedImages(
            imageIds.map((id, index) => ({
              id: `existing-${index}`,
              uri: ConfigAPI.getValidLink(id),
              path: ConfigAPI.getValidLink(id),
              mime: 'image/jpeg',
              filename: `image-${index}.jpg`,
              existingId: id,
            })),
          );
        } else {
          setSelectedImages([]);
        }
      },
      focus: () => {
        inputRef.current?.focus();
      },
    }));

    // Thêm useRef để theo dõi dữ liệu trước đó
    const prevDataRef = useRef<PostData | null>(null);

    // thông báo khi text hoặc ảnh thay đổi
    useEffect(() => {
      // Tạo dữ liệu mới
      const newData = {
        text: text,
        segments: [], // Không còn sử dụng segments
        images: selectedImages,
      };

      // Kiểm tra xem dữ liệu có thay đổi không
      const prevData = prevDataRef.current;
      const hasChanged =
        !prevData ||
        prevData.text !== newData.text ||
        prevData.images !== newData.images;

      // Chỉ gọi callbacks khi dữ liệu thực sự thay đổi
      if (hasChanged) {
        if (onTextChange) {
          onTextChange(text);
        }

        if (onImagesChange) {
          onImagesChange(selectedImages);
        }

        if (onDataChange) {
          onDataChange(newData);
        }

        // Cập nhật ref
        prevDataRef.current = newData;
      }
    }, [text, selectedImages, onTextChange, onImagesChange, onDataChange]);

    // theo dõi selectedImages
    useEffect(() => {
      if (selectedImages.length > 0) {
        setIsResetEditorBackground(true);
        setBackgroundData({
          Id: '0',
          Type: 999,
          Img: '',
          ColorMobile: '',
          TextColor: '',
        });
      } else {
        setIsResetEditorBackground(false);
      }
    }, [selectedImages]);

    useEffect(() => {
      if (initialBackgroundData) {
        setIsResetEditorBackground(true);
        setBackgroundData({...initialBackgroundData});
      } else {
        setIsResetEditorBackground(false);
      }
    }, [initialBackgroundData]);

    // Loại bỏ fullText từ segments

    // Loại bỏ các useEffect liên quan đến mention

    // Loại bỏ toggle format - không cần thiết nữa

    // Chọn màu nền cho bài post
    const handleChooseBackgroundData = (color: BackgroundData) => {
      handleRemoveAllImages();
      setBackgroundData(color);
    };

    // Xử lý hiển thị modal video
    const handleShowVideoModal = () => {
      setShowVideoModal(true);
    };

    // Xử lý thêm link video vào text
    const handleAddVideoLink = (link: string) => {
      const newText = text + (text ? '\n\n' : '') + link;
      setText(newText);
      setShowVideoModal(false);
    };

    const renderEditorDefault = () => {
      return (
        <View style={styles.editorContainer}>
          <ScrollView
            ref={scrollViewRef}
            style={styles.scrollArea}
            keyboardShouldPersistTaps="always"
            onLayout={event => {
              const {height, y} = event.nativeEvent.layout;
              setEditorLayout({height, y});
            }}>
            <TextInput
              ref={inputRef}
              value={text}
              onChangeText={handleTextChange}
              onSelectionChange={handleSelectionChange}
              onLayout={handleTextInputLayout}
              multiline
              selection={selection}
              placeholder="Bạn đang nghĩ gì ?"
              placeholderTextColor={
                ColorThemes.light.Neutral_Text_Color_Subtitle
              }
              cursorColor={ColorThemes.light.Neutral_Text_Color_Body}
              style={styles.simpleTextInput}
              autoCorrect={false}
              autoCapitalize="sentences"
              keyboardType="default"
              textAlignVertical="top"
            />
          </ScrollView>
        </View>
      );
    };

    const renderEditorBackground = () => {
      return (
        <EditorBackground
          value={text}
          background={backgroundData}
          onChangeText={handleTextChange}
          onSelectionChange={handleSelectionChange}
          onLayout={handleTextInputLayout}
        />
      );
    };

    return (
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          style={{flex: 1}}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 120 : 0}>
          {/* Editor */}
          {backgroundData.Type === 999
            ? renderEditorDefault()
            : renderEditorBackground()}

          {/* Pick Color Line */}
          <PickColorLine
            initialBackgroundId={initialBackgroundData?.Id}
            onChoose={handleChooseBackgroundData}
            isReset={isResetEditorBackground}
          />

          {/* Image Preview Section */}
          <ImagePreview
            selectedImages={selectedImages}
            handleRemoveImage={handleRemoveImage}
            handlePickImages={handlePickImages}
            maxImages={maxImages}
          />
          {/* Toolbar */}
          <Toolbar
            handlePickImages={handlePickImages}
            handleShowYouTubeModal={handleShowVideoModal}
          />
        </KeyboardAvoidingView>

        {/* Suggestion Modal cho mention và hashtag */}
        <SuggestionModal
          suggestionState={suggestionState}
          onSelectSuggestion={onSelectSuggestion}
          onClose={hideSuggestion}
        />

        {/* Video Modal */}
        <VideoModal
          visible={showVideoModal}
          onClose={() => setShowVideoModal(false)}
          onAddLink={handleAddVideoLink}
        />
      </SafeAreaView>
    );
  },
);

export default RichTextComposer;
