import { useState, useEffect, useCallback } from 'react';
import GameAudioManager, { GameAudioState } from '../utils/GameAudioManager';

export const useGameAudio = () => {
  const [audioState, setAudioState] = useState<GameAudioState>(() =>
    GameAudioManager.getInstance().getState()
  );
  const [isPreloaded, setIsPreloaded] = useState(false);

  useEffect(() => {
    const gameAudioManager = GameAudioManager.getInstance();

    // Subscribe to audio state changes
    const unsubscribe = gameAudioManager.subscribe(setAudioState);

    // Preload sounds when hook is first used
    console.log('🎵 Starting audio preload...');
    gameAudioManager.preloadSounds()
      .then(() => {
        console.log('🎵 Audio preload completed successfully');
        setIsPreloaded(true);
      })
      .catch(error => {
        console.error('Failed to preload game sounds:', error);
        setIsPreloaded(false);
      });

    // Cleanup subscription on unmount
    return unsubscribe;
  }, []);

  const playCorrectAnswer = useCallback(() => {
    if (!isPreloaded) {
      console.warn('🎵 Audio not preloaded yet, skipping playCorrectAnswer');
      return;
    }
    GameAudioManager.getInstance().playCorrectAnswer();
  }, [isPreloaded]);
   const playWin = useCallback(() => {
    if (!isPreloaded) {
      console.warn('🎵 Audio not preloaded yet, skipping playCorrectAnswer');
      return;
    }
    GameAudioManager.getInstance().playWin();
  }, [isPreloaded]);
  const playInCorrectAnswer = useCallback(() => {
    if (!isPreloaded) {
      console.warn('🎵 Audio not preloaded yet, skipping playCorrectAnswer');
      return;
    }
    GameAudioManager.getInstance().playInCorrectAnswer();
  }, [isPreloaded]);

  const playGameOver = useCallback(() => {
    if (!isPreloaded) {
      console.warn('🎵 Audio not preloaded yet, skipping playGameOver');
      return;
    }
    GameAudioManager.getInstance().playGameOver();
  }, [isPreloaded]);

  const playTimeWarning = useCallback(() => {
    if (!isPreloaded) {
      console.warn('🎵 Audio not preloaded yet, skipping playTimeWarning');
      return;
    }
    GameAudioManager.getInstance().playTimeWarning();
  }, [isPreloaded]);

  const stopCurrentSound = useCallback(() => {
    GameAudioManager.getInstance().stopCurrentSound();
  }, []);

  const stopTimeWarning = useCallback(() => {
    GameAudioManager.getInstance().stopTimeWarning();
  }, []);

  const clearAllSounds = useCallback(() => {
    GameAudioManager.getInstance().clearAllSounds();
  }, []);

  const toggleMute = useCallback(() => {
    return GameAudioManager.getInstance().toggleMute();
  }, []);

  const setMute = useCallback((muted: boolean) => {
    GameAudioManager.getInstance().setMute(muted);
  }, []);

  const isMuted = useCallback(() => {
    return GameAudioManager.getInstance().isMuted();
  }, []);

  return {
    audioState,
    isPreloaded,
    playCorrectAnswer,
    playInCorrectAnswer,
    playGameOver,
    playWin,
    playTimeWarning,
    stopCurrentSound,
    stopTimeWarning,
    clearAllSounds,
    toggleMute,
    setMute,
    isMuted,
  };
};
