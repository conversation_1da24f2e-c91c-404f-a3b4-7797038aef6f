import { useEffect, useState, useCallback, useRef } from 'react';
import { AudioClass, AudioPlayerState } from '../utils/AudioClass';

export interface UseAudioOptions {
  autoSetup?: boolean; // Tự động setup audio khi audioUrl thay đổi
  autoCleanup?: boolean; // Tự động cleanup khi component unmount
}

export interface UseAudioReturn {
  // State
  isPlaying: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  playAudio: (loop?: boolean) => Promise<void>;
  stopAudio: () => void;
  pauseAudio: () => void;
  resumeAudio: () => void;
  setupAudio: (url: string) => Promise<void>;
  
  // Advanced controls
  setVolume: (volume: number) => void;
  getDuration: () => number;
  getCurrentTime: () => Promise<number>;
  setCurrentTime: (time: number) => void;
  
  // Cleanup
  dispose: () => void;
}

export const useAudio = (
  audioUrl?: string | null,
  options: UseAudioOptions = {}
): UseAudioReturn => {
  const { autoSetup = true, autoCleanup = true } = options;
  
  const audioRef = useRef<AudioClass | null>(null);
  const [state, setState] = useState<AudioPlayerState>({
    isPlaying: false,
    isLoading: false,
    error: null,
  });

  // Khởi tạo AudioClass
  useEffect(() => {
    if (!audioRef.current) {
      audioRef.current = new AudioClass((newState) => {
        setState(newState);
      });
    }

    return () => {
      if (autoCleanup && audioRef.current) {
        audioRef.current.dispose();
        audioRef.current = null;
      }
    };
  }, [autoCleanup]);

  // Auto setup audio khi URL thay đổi
  useEffect(() => {
    if (autoSetup && audioUrl && audioRef.current) {
      audioRef.current.setupAudio(audioUrl).catch((error) => {
        console.error('Auto setup audio failed:', error);
      });
    }
  }, [audioUrl, autoSetup]);

  // Actions
  const playAudio = useCallback(async (loop: boolean = false) => {
    if (audioRef.current) {
      await audioRef.current.playAudio(loop);
    }
  }, []);

  const stopAudio = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.stopAudio();
    }
  }, []);

  const pauseAudio = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pauseAudio();
    }
  }, []);

  const resumeAudio = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.resumeAudio();
    }
  }, []);

  const setupAudio = useCallback(async (url: string) => {
    if (audioRef.current) {
      await audioRef.current.setupAudio(url);
    }
  }, []);

  const setVolume = useCallback((volume: number) => {
    if (audioRef.current) {
      audioRef.current.setVolume(volume);
    }
  }, []);

  const getDuration = useCallback(() => {
    return audioRef.current ? audioRef.current.getDuration() : 0;
  }, []);

  const getCurrentTime = useCallback(async () => {
    return audioRef.current ? await audioRef.current.getCurrentTime() : 0;
  }, []);

  const setCurrentTime = useCallback((time: number) => {
    if (audioRef.current) {
      audioRef.current.setCurrentTime(time);
    }
  }, []);

  const dispose = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.dispose();
      audioRef.current = null;
    }
  }, []);

  return {
    // State
    isPlaying: state.isPlaying,
    isLoading: state.isLoading,
    error: state.error,
    
    // Actions
    playAudio,
    stopAudio,
    pauseAudio,
    resumeAudio,
    setupAudio,
    
    // Advanced controls
    setVolume,
    getDuration,
    getCurrentTime,
    setCurrentTime,
    
    // Cleanup
    dispose,
  };
};
