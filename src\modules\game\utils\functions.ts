function replaceObjectById(newObj: any, array: any) {
  const cloneArray = [...array];
  const index = array.findIndex((item: any) => item.id === newObj.id);
  if (index !== -1) {
    cloneArray[index] = newObj;
  }
  return cloneArray;
}

// Kiểm tra object có sort lớn nhất trong mảng không
function hasMaxSort(arr: any[], obj: any) {
  // Kiểm tra nếu mảng rỗng hoặc object không có thuộc tính sort
  if (!arr || arr.length === 0 || typeof obj.sort !== 'number') {
    return false;
  }

  // Tìm giá trị Sort lớn nhất trong mảng
  const maxSort = Math.max(...arr.map(item => item.sort || 0));

  // So sánh với Sort của object truyền vào
  return obj.sort === maxSort;
}

// Kiểm tra list object đưa vào có đúng thứ tự tăng dần không
const checkPositionOrder = (arr: any[]) => {
  if (arr.length <= 1) {
    return true;
  }

  for (let i = 0; i < arr.length - 1; i++) {
    if (arr[i].sort > arr[i + 1].sort) {
      return false;
    }
  }

  return true;
};

// Kiểm tra đáp án SakuLC với logic IsResult và Sort
const checkSakuLCAnswer = (userWords: any[]) => {
  if (!userWords || userWords.length === 0) {
    console.log('No user words provided');
    return false;
  }

  console.log('=== CHECKING SAKULC ANSWER ===');
  console.log('User words:', userWords.map(w => ({ text: w.text, IsResult: w.IsResult, correctPosition: w.correctPosition })));

  // 1. Kiểm tra có từ gây nhiễu không (IsResult != true)
  const hasDistractorWords = userWords.some(word => word.IsResult !== true);
  if (hasDistractorWords) {
    console.log('Found distractor words (IsResult != true) in answer');
    return false;
  }

  // 2. Tất cả từ phải có IsResult = true
  const allCorrectWords = userWords.filter(word => word.IsResult === true);
  if (allCorrectWords.length !== userWords.length) {
    console.log('Not all words have IsResult = true');
    return false;
  }

  // 3. Kiểm tra thứ tự Sort tăng dần theo vị trí user sắp xếp
  for (let i = 0; i < userWords.length - 1; i++) {
    const currentSort = userWords[i].correctPosition; // correctPosition chính là Sort từ API
    const nextSort = userWords[i + 1].correctPosition;

    if (currentSort >= nextSort) {
      console.log(`Sort order incorrect: position ${i} has sort ${currentSort}, position ${i + 1} has sort ${nextSort}`);
      return false;
    }
  }

  console.log('Answer is correct - all words have IsResult=true and Sort in ascending order');
  return true;
};

// Xoá object trong array theo type
function removeObjectByType(array: any[], objectToRemove: any, type: string) {
  return array.filter(item => item[type] !== objectToRemove[type]);
}

export {
  replaceObjectById,
  checkPositionOrder,
  removeObjectByType,
  hasMaxSort,
  checkSakuLCAnswer,
};
