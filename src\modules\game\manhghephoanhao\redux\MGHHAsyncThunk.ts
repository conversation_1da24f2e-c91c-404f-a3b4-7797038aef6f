import {createAsyncThunk} from '@reduxjs/toolkit';
import {GetGameConfigRequest} from '../../sakutimban/types/sakuTBTypes';
import {MghhDa} from '../da/MghhDa';

const mghhDa = new MghhDa();

const loadInitData = createAsyncThunk(
  'MGHHReducer/loadInitData',
  async ({gameId, stage,
    competenceId,}: {
      gameId: string;
      stage: number;
      competenceId: string;
    }) => {
    const gameConfig = await MghhDa.getGameConfig(gameId);
    const questions = await mghhDa.getQuestionsByGameAndStage(
        gameId,
        stage,
        competenceId,
      );
      debugger
    return {
      gameConfig,
      questions,
    };
  },
);

export {loadInitData};
