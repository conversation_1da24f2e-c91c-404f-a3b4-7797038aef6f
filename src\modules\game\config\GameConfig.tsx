// Actual Generic Game Home Screen Component
import React from 'react';
import Config<PERSON><PERSON> from '../../../Config/ConfigAPI';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';

// Generic Game Home Screen Configuration Interface
export interface GameConfig {
  // Basic Info
  gameId: string;
  gameName: string;

  // Assets
  backgroundImage: any;
  audio?: string;
  milestoneImages: {
    completed: any;
    inProgress: any;
    locked: any;
  };
  birdImage?: any;
  footerIcons: {
    coin: any;
    rank: any;
    level: any;
    help: any;
  };

  // Content
  modalContent: string;
  helpText: string;

  // Navigation
  startGameScreen: string;
  startGameComponent?: React.ComponentType<any>;

  // Styling
  colors: {
    primary: string;
    footer: string;
    text: string;
  };

  // Game-specific Logic
  milestonePositions?: Array<{
    id: number;
    top: number;
    left: number;
  }>;

  // Custom hooks (optional)
  useSVGPath?: (params: any) => any;
  usePathTrail?: (params: any) => any;
}

// Default configurations for different games
export const GAME_CONFIGS: Record<string, GameConfig> = {
  // Game ALTP
  cf86bc33ef03447fa744eea2bbf31cfc: {
    gameId: ConfigAPI.gameALTP,
    gameName: 'Saku Triệu Phú',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'),
    audio: 'mucsic_bg.mp3',
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Game kiến thức tổng hợp\n (tiếng Nhật, văn hóa)\nChọn đáp án đúng',
    helpText:
      '- Quy tắc: Bạn cần vượt qua 15 câu hỏi. Có 3 cột mốc quan trọng là 5,10,15. Bạn có 4 sự trợ giúp: 50/50, hỏi ý kiến khán giả, hỏi ý kiến chuyên gia, gọi điện thoại cho người thân. Nếu bạn chọn câu trả lời sai game sẽ kết thúc.\n- Chế độ thưởng: vượt qua câu số 5 được 40 điểm; vượt qua câu số 10 được 130 điểm; vượt qua câu số 15 được 300 điểm',
    startGameScreen: 'StartALTP',
    colors: {
      primary: '#112164',
      footer: '#FF5757',
      text: '#fff',
    },
  },

  '7eb45b0edc6247c3bde6bbb15547dfda': {
    gameId: ConfigAPI.gameSakuTB,
    gameName: 'Saku Tìm Bạn',
    backgroundImage: require('../sakutimban/assets/Image-saku-tim-ban.png'),
    audio: 'music_bg_chung.mp3',
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Game từ vựng, chữ Hán\nChọn cặp từ tương ứng ở mỗi \n cột  để tạo đáp án đúng',
    helpText:
      '- Quy tắc: Bạn cần vượt qua 20 câu hỏi/ 1 chặng. Bạn có 5 phút và 3 mạng . Mỗi lần trả lời sai bạn bị trừ 1 mạng. Game kết thúc khi hết mạng hoặc hết thời gian.\n- Chế độ thưởng: 20điểm x số mạng còn lại; nếu giữ nguyên 3 mạng bạn được thưởng 50 điểm.',
    startGameScreen: 'StartSakuTB',
    colors: {
      primary: '#2E7D32',
      footer: '#FF5757',
      text: '#fff',
    },
  },

  '1d56852db9964d9a878a1d9d5f872cb7': {
    gameId: '1d56852db9964d9a878a1d9d5f872cb7',
    gameName: 'Saku Chơi Chữ',
    backgroundImage: require('../duoihinhbatchu/assets/Image-DHBC.png'),
    audio: 'music_bg_chung.mp3',
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Game từ vựng, chữ Hán \nNhập từ tương ứng với \n hình ảnh gợi ý',
    helpText:
      '- Quy tắc: Bạn cần vượt qua 15 câu hỏi/ 3 cấp độ/ 1 chặng. Thời gian chơi mỗi cấp: 150s, 100s, 75s. Bạn có 3 mạng. Mỗi lần trả lời sai bạn bị trừ 1 mạng.  Game kết thúc khi hết mạng hoặc hết thời gian.\n- Chế độ thưởng: vượt qua cấp độ 1 được 20 điểm; cấp độ 1 được 40 điểm; cấp độ 1 được 60 điểm. Điểm mạng: 20điểm x số mạng còn lại',
    startGameScreen: 'StartDHBC',
    colors: {
      primary: '#7B1FA2',
      footer: '#FF5757',
      text: '#fff',
    },
  },
  '1b1804be1c6049c2876d1626794fa7a0': {
    gameId: '1b1804be1c6049c2876d1626794fa7a0',
    gameName: 'Saku Ghép Hình',
    backgroundImage: require('../manhghephoanhao/assets/Image-MGHH.png'),
    audio: 'music_bg_chung.mp3',
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Game ngữ pháp \nSắp xếp câu hoàn chỉnh\n dựa vào các từ gợi ý',
    helpText:
      '- Quy tắc: Bạn cần vượt qua 15 câu hỏi/ 3 cấp độ/ 1 chặng. Thời gian chơi mỗi cấp: 100s, 75s, 50s. Sử dụng gợi ý bị trừ 5 điểm. Game kết thúc khi hết mạng hoặc hết thời gian.\n- Chế độ thưởng: Điểm mỗi cấp độ 20 điểm, 40 điểm, 60 điểm',
    startGameScreen: 'StartMGHH',
    colors: {
      primary: '#7B1FA2',
      footer: '#FF5757',
      text: '#fff',
    },
  },
  '769bce29753d4fa48314325c4bc7ebb0': {
    gameId: '769bce29753d4fa48314325c4bc7ebb0',
    gameName: 'Saku Luyện Công',
    backgroundImage: require('../sakuluyencong/assets/Image-saku-luyen-cong.png'),
    audio: 'music_bg_chung.mp3',
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Game nghe hiểu\n Sắp xếp câu tương ứng\n với file âm thanh nghe được',
    helpText:
      '- Quy tắc: Bạn cần vượt qua 15 câu hỏi/ 1 chặng. Bạn có 5 phút và 5 mạng. Mỗi lần trả lời sai bạn bị trừ 1 mạng. Sử dụng gợi ý bị trừ 5 điểm. Game kết thúc khi hết mạng hoặc hết thời gian.\n- Chế độ thưởng:  15điểm x số mạng còn lại; nếu giữ nguyên 5 mạng bạn được thưởng 50 điểm.',
    startGameScreen: 'StartSakuLC',
    colors: {
      primary: '#7B1FA2',
      footer: '#FF5757',
      text: '#fff',
    },
  },

  '19d8c9d61ae74f968416b28fcf8e93c3': {
    gameId: ConfigAPI.gameSKXT,
    gameName: 'Saku Xây Tổ',
    backgroundImage: require('../sakuxayto/assets/Image-saku-xay-to.png'),
    audio: 'music_bg_chung.mp3',
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Game ngữ pháp \nSắp xếp từ vào vị trí thích hợp\n để tạo thành câu hoàn chỉnh',
    helpText:
      '"- Quy tắc: Bạn cần vượt qua 15 câu hỏi/ 1 chặng. Bạn có 5 phút và 3 mạng. Mỗi lần trả lời sai bạn bị trừ 1 mạng. Sử dụng gợi ý bị trừ 5 điểm. Game kết thúc khi hết mạng hoặc hết thời gian.\n- Chế độ thưởng:  20điểm x số mạng còn lại; nếu giữ nguyên 3 mạng bạn được thưởng 50 điểm."',
    startGameScreen: 'StartSakuXT',
    colors: {
      primary: '#7B1FA2',
      footer: '#FF5757',
      text: '#fff',
    },
  },
  '05ac80f4e3b54615afb06d49f5140ade': {
    gameId: ConfigAPI.gameSakusanmoi,
    gameName: 'Saku Săn Mồi',
    backgroundImage: require('../sakusanmoi/assets/Image-saku-san-moi.png'),
    audio: 'music_bg_chung.mp3',
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Game từ vựng, ngữ pháp\nChọn đáp án phù hợp trong ( )',
    helpText:
      '- Quy tắc: Bạn cần vượt qua 20 câu hỏi/ 1 chặng. Bạn có 5 phút và 3 mạng. Mỗi lần trả lời sai bạn bị trừ 1 mạng. Sử dụng gợi ý bị trừ 5 điểm. Game kết thúc khi hết mạng hoặc hết thời gian.\n- Chế độ thưởng:  20điểm x số mạng còn lại; nếu giữ nguyên 3 mạng bạn được thưởng 50 điểm.',
    startGameScreen: 'StartSakuSM',
    colors: {
      primary: '#7B1FA2',
      footer: '#FF5757',
      text: '#fff',
    },
  },
  e856cf006f2745bda1b21dba65df4d71: {
    gameId: ConfigAPI.gameVTNV,
    gameName: 'Saku Vượt Ải',
    backgroundImage: require('../vuotchuongngaivat/assets/Image-VCNV.png'),
    audio: 'music_bg_chung.mp3',
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Nhập từ thích hợp để giải mã\n ô chữ hàng ngang và hàng dọc',
    helpText:
      '"- Quy tắc: Bạn cần trả lời các câu hỏi để giải mã ô chữ hàng ngang và hàng dọc. Ô hàng dọc là từ khóa chính. Ô hàng ngang là từ khóa phụ. Bạn có 10 phút để giải mã ô chữ.\n- Chế độ thưởng:  Giải mã ô chữ hàng dọc: 50 điểm. Ô hàng ngang 10 điểm x số câu"',
    startGameScreen: 'StartVCNV',
    colors: {
      primary: '#7B1FA2',
      footer: '#FF5757',
      text: '#fff',
    },
  },
  c0b7fbb981bf495295de84d6b6b008c7: {
    gameId: ConfigAPI.gameSakuTC,
    gameName: 'Saku Chuyền Cành',
    backgroundImage: require('../sakuchuyencanh/assets/Image-saku-truyen-canh.png'),
    audio: 'music_bg_chung.mp3',
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Kéo thả đáp án vào cành cây\n theo thứ tự để tạo thành câu\nhoàn chỉnh',
    helpText:
      '- Quy tắc: Bạn cần vượt qua 15 câu hỏi/ 3 cấp độ/ 1 chặng. Thời gian chơi mỗi cấp: 100s, 75s, 50s. Sử dụng gợi ý bị trừ 5 điểm. Game kết thúc khi hết mạng hoặc hết thời gian.\n- Chế độ thưởng: vượt qua cấp độ 1 được 20 điểm; cấp độ 1 được 40 điểm; cấp độ 1 được 60 điểm. Điểm mạng: 20điểm x số mạng còn lại',
    startGameScreen: 'StartSakuTC',
    colors: {
      primary: '#7B1FA2',
      footer: '#FF5757',
      text: '#fff',
    },
  },

  // Có thể thêm các game khác...
};

// Props interface for Generic Home Screen
export interface GenericGameHomeScreenProps {
  gameConfig: GameConfig;
  // Override specific components if needed
  HeaderComponent?: React.ComponentType<any>;
  FooterComponent?: React.ComponentType<any>;
  MilestoneComponent?: React.ComponentType<any>;
  StartGameModalComponent?: React.ComponentType<any>;

  // Custom handlers
  onMilestonePress?: (
    status: string,
    number: number,
    levelName?: string,
  ) => void;
  onLevelSelect?: (level: any) => void;

  // Additional props
  customStyles?: any;
  customHooks?: {
    useSVGPath?: (params: any) => any;
    usePathTrail?: (params: any) => any;
  };
}

// Utility function to get game config
export const getGameConfig = (gameId: string): GameConfig => {
  const config = GAME_CONFIGS[gameId];
  if (!config) {
    showSnackbar({
      message: 'Trò chơi này đang được phát triển',
      status: ComponentStatus.INFOR,
    });
    return getGameConfig('ALTP');
    // throw new Error(`Game config not found for gameId: ${gameId}`);
  }
  return config;
};

// Utility function to merge custom config with default
export const mergeGameConfig = (
  gameId: string,
  customConfig: Partial<GameConfig>,
): GameConfig => {
  const defaultConfig = getGameConfig(gameId);
  return {
    ...defaultConfig,
    ...customConfig,
    // Deep merge for nested objects
    milestoneImages: {
      ...defaultConfig.milestoneImages,
      ...customConfig.milestoneImages,
    },
    footerIcons: {
      ...defaultConfig.footerIcons,
      ...customConfig.footerIcons,
    },
    colors: {
      ...defaultConfig.colors,
      ...customConfig.colors,
    },
  };
};
