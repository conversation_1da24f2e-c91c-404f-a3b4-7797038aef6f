import React, {createContext, useContext} from 'react';

// Create context for floating button
export interface FloatingButtonContextType {
  isFloatingButtonVisible: boolean;
  showFloatingButton: () => void;
  hideFloatingButton: () => void;
}

export const FloatingButtonContext = createContext<FloatingButtonContextType | undefined>(undefined);

export const useFloatingButton = () => {
  const context = useContext(FloatingButtonContext);
  if (!context) {
    throw new Error('useFloatingButton must be used within FloatingButtonProvider');
  }
  return context;
};
