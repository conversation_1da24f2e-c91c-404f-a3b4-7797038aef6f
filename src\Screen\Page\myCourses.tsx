import { Tab<PERSON><PERSON>, TabView } from 'react-native-tab-view';
import TitleWithBottom from '../Layout/titleWithBottom';
import { ColorThemes } from '../../assets/skin/colors';
import { TouchableOpacity, useWindowDimensions, View } from 'react-native';
import { useState } from 'react';
import { DrawerActions, useNavigation, useRoute } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../../redux/store/store';
import PurchasedCourse from '../../modules/Course/listview/purchasedCourse';
import { TypoSkin } from '../../assets/skin/typography';
import TryingTests from '../../modules/exam/views/tryingTest/tryingTests';
import Certificates from '../../modules/Course/listview/certificates';
import FlashCards from '../../modules/flashcard/flashCard';
import { Winicon } from 'wini-mobile-components';
import { LogoImg } from './Home';
import TitleWithImage from '../Layout/titleWithImage';
import { navigate, RootScreen } from '../../router/router';

export default function MyCourses() {
  const [index, setIndex] = useState(0);
  const layout = useWindowDimensions();

  const renderTabBar = (props: any) => (
    <TabBar
      {...props}
      activeColor={ColorThemes.light.Primary_Color_Main}
      indicatorStyle={{
        backgroundColor: ColorThemes.light.Primary_Color_Main,
        height: 1.5,
      }}
      onTabPress={() => { }}
      tabStyle={{ paddingHorizontal: 4, paddingTop: 0 }}
      inactiveColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
      style={{
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        height: 40,
        elevation: 0,
      }}
      labelStyle={{ ...TypoSkin.label4 }}
    />
  );

  const renderScene = ({ route }: any) => {
    switch (route.key) {
      case 'course':
        return <PurchasedCourse />;
      // case 'test':
      //   return <TryingTests />;

      case 'flashcard':
        return <FlashCards />;
      case 'verification':
        return <Certificates />;
      default:
        return null;
    }
  };

  const routes = [
    { key: 'course', title: 'Khóa học' },
    // {key: 'test', title: 'Test'},
    { key: 'flashcard', title: 'FlashCard' },
    { key: 'verification', title: 'Chứng nhận' },

  ];
  const navigation = useNavigation<any>();

  return (
    <TitleWithImage
      iconActionPress={() => {
        navigate(RootScreen.Notification);
      }}
      prefix={
        <TouchableOpacity
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
          style={{ padding: 4 }}>
          <LogoImg />
        </TouchableOpacity>
      }
      title="Học tập">
      <TabView
        navigationState={{ index, routes }}
        renderScene={renderScene}
        renderTabBar={renderTabBar}
        swipeEnabled={false}
        onIndexChange={setIndex}
        initialLayout={{ width: layout.width }}
      />
    </TitleWithImage>
  );
}
