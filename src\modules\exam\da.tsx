import {DataController} from '../../base/baseController';
import ConfigAPI from '../../Config/ConfigAPI';
import {ExamType, StorageContanst, TestType} from '../../Config/Contanst';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
export class examDA {
  private answerController: DataController;
  private testController: DataController;
  private examController: DataController;
  private questionController: DataController;
  private resulttestController: DataController;
  private cusId: string | null = null;

  constructor(
    answerController = new DataController('Answer'),
    examController = new DataController('Exam'),
    testController = new DataController('Test'),
    resulttestController = new DataController('Test_Result'),
    questionController = new DataController('Question'),
  ) {
    this.answerController = answerController;
    this.examController = examController;
    this.testController = testController;
    this.resulttestController = resulttestController;
    this.questionController = questionController;
  }
  private async getCustomerId(): Promise<string | null> {
    if (!this.cusId) {
      this.cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    }
    return this.cusId;
  }
  async getListAnswer(id: string) {
    const respone = await this.answerController.getListSimple({
      query: `@QuestionId: {${id}}`,
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getListAnswerbyListQuestion(id: string[]) {
    const respone = await this.answerController.getListSimple({
      query: `@QuestionId: {${id.join(' | ')}}`,
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getListExam(type: number) {
    const respone = await this.examController.getListSimple({
      query: `@Type: [${type}]`,
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getListExamTry({page, size}: {page: number; size: number}) {
    var respone = await this.testController.getPatternList({
      page: page ?? 1,
      size: size ?? 10,
      query: `@Type: [${TestType.Try}]`,
      // query: `(@Type: [${
      //   TestType.Try
      // }] (@DateStart:[-inf ${new Date().getTime()}] @DateEnd:[${new Date().getTime()} +inf])) | (@Type: [${
      //   TestType.Try
      // }] @DateEnd:[-inf ${new Date().getTime()}])`,
      pattern: {
        SectionId: ['Id', 'Name', 'Time', 'Score'],
        Exam: {
          searchRaw: `*`,
          reducers: `APPLY \"split(@QuestionId)\" AS qid GROUPBY 2 @TestId @qid REDUCE COUNT 0 AS count GROUPBY 1 @TestId  REDUCE SUM 1 @count AS totalQuestions`,
        },
      },
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getTestbyId(id?: string) {
    var respone = await this.testController.getPatternList({
      query: `@Id: {${id}}`,
      pattern: {
        SectionId: ['Id', 'Name', 'Time', 'Score'],
        Exam: {
          searchRaw: `*`,
          reducers: `APPLY \"split(@QuestionId)\" AS qid GROUPBY 2 @TestId @qid REDUCE COUNT 0 AS count GROUPBY 1 @TestId  REDUCE SUM 1 @count AS totalQuestions`,
        },
      },
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getListExamReal({page, size}: {page: number; size: number}) {
    var respone = await this.testController.getPatternList({
      page: page ?? 1,
      size: size ?? 10,
      query: `@Type: [${TestType.Real}]`,
      // query: `@Type: [${
      //   TestType.Real
      // }] (@DateStart:[-inf ${new Date().getTime()}] @DateEnd:[${new Date().getTime()} +inf])`,
      pattern: {
        SectionId: ['Id', 'Name', 'Time', 'Score'],
        Exam: {
          searchRaw: `*`,
          reducers: `APPLY \"split(@QuestionId)\" AS qid GROUPBY 2 @TestId @qid REDUCE COUNT 0 AS count GROUPBY 1 @TestId  REDUCE SUM 1 @count AS totalQuestions`,
        },
      },
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getListExamHistory({ids}: {ids: string[]}) {
    var cusId = await this.getCustomerId();
    const respone = await this.testController.getListSimple({
      query: `@CustomerId: {${cusId}} @Type:[${
        ExamType.Try
      }] @ExamId:{${ids.join(' | ')}}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getListExambyLessonId(id: string) {
    const respone = await this.examController.getListSimple({
      query: `@LessonId: {${id}} @Type: [${ExamType.Real}]`,
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getListQuizbyLessonId(id: string) {
    const respone = await this.examController.getListSimple({
      query: `@LessonId: {${id}} @Type: [${ExamType.quiz}]`,
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getListQuestionbyExamId(id: string) {
    const respone = await this.examController.getPatternList({
      query: `@Id: {${id}}`,
      pattern: {
        QuestionId: [
          'Id',
          'Name',
          'Title',
          'SelectionType',
          'Score',
          'Audio',
          'Img',
          'Content',
        ],
      },
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getTestResultbyId(id: string) {
    const respone = await this.resulttestController.getById(id);
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
}
