import Sound from 'react-native-sound';

export interface GameAudioState {
  isPlaying: boolean;
  currentTrack: string | null;
  isMuted: boolean;
}

class GameAudioManager {
  private static instance: GameAudioManager;
  private sounds: Map<string, Sound> = new Map();
  private currentState: GameAudioState = {
    isPlaying: false,
    currentTrack: null,
    isMuted: false,
  };
  private listeners: ((state: GameAudioState) => void)[] = [];
  private instanceId: string;

  private constructor() {
    // Enable playback in silence mode (iOS)
    Sound.setCategory('Playback');
    this.instanceId = Math.random().toString(36).substr(2, 9);
    console.log(`🎵 GameAudioManager instance created with ID: ${this.instanceId}`);
  }

  public static getInstance(): GameAudioManager {
    if (!GameAudioManager.instance) {
      console.log('🎵 Creating new GameAudioManager instance');
      GameAudioManager.instance = new GameAudioManager();
    } else {
      console.log('🎵 Returning existing GameAudioManager instance');
    }
    return GameAudioManager.instance;
  }

  public subscribe(listener: (state: GameAudioState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentState));
  }

  private updateState(newState: Partial<GameAudioState>): void {
    this.currentState = { ...this.currentState, ...newState };
    this.notifyListeners();
  }

  public async preloadSounds(): Promise<void> {
    // Use direct main bundle paths (files should be in android/app/src/main/res/raw/)
    const soundFiles = [
      { key: 'correct', path: 'correct.mp3' },
      { key: 'incorrect', path: 'incorrect.mp3' },
      { key: 'win', path: 'win.mp3' },
      { key: 'gameOver', path: 'game_over.mp3' },
      { key: 'timeWarning', path: 'hetgio.mp3' }, // Updated to hetgio as you mentioned
    ];

    const loadPromises = soundFiles.map(({ key, path }) => {
      return new Promise<void>((resolve, reject) => {
        console.log(`Loading sound: ${key} from MAIN_BUNDLE path: ${path}`);

        // Load directly from main bundle (Android: res/raw/, iOS: main bundle)
        const sound = new Sound(path, Sound.MAIN_BUNDLE, (error) => {
          if (error) {
            console.error(`Failed to load ${key} sound from MAIN_BUNDLE:`, error);
            console.error(`Error details:`, error.message, error.code);

            // Try without .mp3 extension (Android sometimes needs this)
            const pathWithoutExt = path.replace('.mp3', '');
            console.log(`Trying without extension: ${pathWithoutExt}`);

            const fallbackSound = new Sound(pathWithoutExt, Sound.MAIN_BUNDLE, (fallbackError) => {
              if (fallbackError) {
                console.error(`Failed to load ${key} sound without extension:`, fallbackError);
                reject(fallbackError);
                return;
              }

              this.sounds.set(key, fallbackSound);
              console.log(`${key} sound loaded successfully (without extension)`);
              resolve();
            });
            return;
          }

          this.sounds.set(key, sound);
          console.log(`${key} sound loaded successfully from MAIN_BUNDLE`);
          resolve();
        });
      });
    });

    try {
      await Promise.all(loadPromises);
      this.setMute(false);
      console.log('All game sounds preloaded successfully');
      console.log('Final loaded sounds:', Array.from(this.sounds.keys()));
    } catch (error) {
      console.error('Failed to preload some sounds:', error);
    }
  }

  public playCorrectAnswer(): void {
    console.log(`🎵 GameAudioManager.playCorrectAnswer() called on instance: ${this.instanceId}`);
    if (this.currentState.isMuted) {
      console.log('🔇 Audio is muted, skipping playCorrectAnswer');
      return;
    }
    this.playSound('correct', false);
  }
  public playInCorrectAnswer(): void {
    console.log(`🎵 GameAudioManager.playCorrectAnswer() called on instance: ${this.instanceId}`);
    if (this.currentState.isMuted) {
      console.log('🔇 Audio is muted, skipping playCorrectAnswer');
      return;
    }
    this.playSound('incorrect', false);
  }
  public playWin(): void {
    console.log(`🎵 GameAudioManager.playCorrectAnswer() called on instance: ${this.instanceId}`);
    if (this.currentState.isMuted) {
      console.log('🔇 Audio is muted, skipping playCorrectAnswer');
      return;
    }
    this.playSound('win', false);
  }

  public playGameOver(): void {
    console.log(`💀 GameAudioManager.playGameOver() called on instance: ${this.instanceId}`);
    if (this.currentState.isMuted) {
      console.log('🔇 Audio is muted, skipping playGameOver');
      return;
    }
    this.playSound('gameOver', false);
  }

  public playTimeWarning(): void {
    console.log(`⏰ GameAudioManager.playTimeWarning() called on instance: ${this.instanceId}`);
    if (this.currentState.isMuted) {
      console.log('🔇 Audio is muted, skipping playTimeWarning');
      return;
    }
    this.playSound('timeWarning', true); // Loop for urgency
  }

  public toggleMute(): boolean {
    const newMutedState = !this.currentState.isMuted;
    console.log(`🔇 Toggling mute: ${this.currentState.isMuted} -> ${newMutedState}`);

    if (newMutedState && this.currentState.isPlaying) {
      // If muting and something is playing, stop it
      this.stopCurrentSound();
    }

    this.updateState({ isMuted: newMutedState });
    return newMutedState;
  }

  public setMute(muted: boolean): void {
    console.log(`🔇 Setting mute to: ${muted}`);

    if (muted && this.currentState.isPlaying) {
      // If muting and something is playing, stop it
      this.stopCurrentSound();
    }

    this.updateState({ isMuted: muted });
  }

  public isMuted(): boolean {
    return this.currentState.isMuted;
  }

  private playSound(key: string, loop: boolean = false): void {
    console.log(`GameAudioManager[${this.instanceId}]: Attempting to play sound: ${key}, loop: ${loop}`);
    console.log(`GameAudioManager[${this.instanceId}]: Available sounds:`, Array.from(this.sounds.keys()));

    const sound = this.sounds.get(key);
    if (!sound) {
      console.warn(`GameAudioManager: Sound ${key} not found in loaded sounds`);
      return;
    }

    console.log(`GameAudioManager: Sound ${key} found, attempting to play`);

    // Stop current playing sound if any
    this.stopCurrentSound();

    this.updateState({ isPlaying: true, currentTrack: key });

    if (loop) {
      sound.setNumberOfLoops(-1);
      console.log(`GameAudioManager: Set ${key} to loop infinitely`);
    } else {
      sound.setNumberOfLoops(0);
      console.log(`GameAudioManager: Set ${key} to play once`);
    }

    sound.play((success) => {
      if (!loop) {
        this.updateState({ isPlaying: false, currentTrack: null });
      }

      if (success) {
        console.log(`GameAudioManager: ${key} sound played successfully`);
      } else {
        console.error(`GameAudioManager: Failed to play ${key} sound`);
        this.updateState({ isPlaying: false, currentTrack: null });
      }
    });
  }

  public stopCurrentSound(): void {
    if (this.currentState.currentTrack) {
      const sound = this.sounds.get(this.currentState.currentTrack);
      if (sound) {
        sound.stop(() => {
          console.log(`Stopped ${this.currentState.currentTrack} sound`);
        });
      }
      this.updateState({ isPlaying: false, currentTrack: null });
    }
  }

  public stopTimeWarning(): void {
    const timeWarningSound = this.sounds.get('timeWarning');
    if (timeWarningSound) {
      timeWarningSound.stop(() => {
        console.log('Time warning sound stopped');
      });
      if (this.currentState.currentTrack === 'timeWarning') {
        this.updateState({ isPlaying: false, currentTrack: null });
      }
    }
  }

  public clearAllSounds(): void {
    console.log(`🗑️ GameAudioManager[${this.instanceId}]: Clearing all sounds`);
    console.log(`🗑️ Sounds to clear:`, Array.from(this.sounds.keys()));

    // Stop all sounds
    this.sounds.forEach((sound, key) => {
      sound.stop();
      sound.release();
      console.log(`Released ${key} sound`);
    });

    this.sounds.clear();
    this.updateState({ isPlaying: false, currentTrack: null });
    console.log(`🗑️ GameAudioManager[${this.instanceId}]: All game sounds cleared`);
  }

  public getState(): GameAudioState {
    return { ...this.currentState };
  }
}

export default GameAudioManager;
