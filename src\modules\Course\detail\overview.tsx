/* eslint-disable react-native/no-inline-styles */
import {
  Dimensions,
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {RenderHTML} from 'react-native-render-html';
import ConfigAPI from '../../../Config/ConfigAPI';
import {TypoSkin} from '../../../assets/skin/typography';
import {
  FBottomSheet,
  HashTag,
  hideBottomSheet,
  ListTile,
  Rating,
  showBottomSheet,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {useEffect, useRef, useState} from 'react';
import {SocialDA} from '../../community/news/da';
import {CustomerDA} from '../../customer/da';
import {Ultis} from '../../../utils/Utils';
import {CourseDA} from '../da';
import {useTranslation} from 'react-i18next';
import CourseAslsoLike from './courseAslsoLike';
import {useRatingData} from '../../../redux/hook/ratingHook';
import {navigate, RootScreen} from '../../../router/router';
import FastImage from '@d11/react-native-fast-image';

export default function Overview(pros: any) {
  const [listTags, setlistTags] = useState<Array<any>>();
  const [data, setData] = useState<any>(pros.data);
  const {width} = useWindowDimensions();
  const [countStudent, setcountStudent] = useState<any>([]);
  const [countLesson, setcountLesson] = useState(0);
  const [totalHours, settotalHours] = useState(0);
  const {t} = useTranslation();
  const {ratings} = useRatingData(pros.data.Id);
  const courseDA = new CourseDA();
  const customerDA = new CustomerDA();
  const socialDA = new SocialDA();
  const [totalDocuments, setTotalDocuments] = useState(0);
  const bottomSheetRef = useRef<any>(null);

  useEffect(() => {
    getlistTags();
    getAuthor();
    getcountStudent();
    getLesson();
  }, [pros.refreshControlProps]);

  const getlistTags = async () => {
    const result = await socialDA.getListTopic(data?.TopicId);
    if (result) {
      setlistTags(result.data);
    }
  };

  const getAuthor = async () => {
    const result = await customerDA.getCustomerbyId(data?.CustomerId);
    if (result) {
      data.relativeUser = {
        image: result.data.AvatarUrl,
        title: `${result.data.Name} ${Ultis.getDiffrentTime(
          data?.DateCreated,
        )}`,
      };
    }
  };

  const getcountStudent = async () => {
    const result = await courseDA.studentsCourse(data?.Id);
    setcountStudent(result);
  };

  const getLesson = async () => {
    const result = await courseDA.getLessonbyCourseId(data?.Id);
    if (result) {
      setcountLesson(result?.length ?? 0);
      let totalDocuments = 0;
      result.forEach((lesson: any) => {
        if (lesson.Document) {
          try {
            // Check if Document is a JSON string containing multiple documents
            const documents = lesson.Document?.split(',');
            if (Array.isArray(documents)) {
              totalDocuments += documents.length;
            } else {
              // If not an array but Document exists, count as 1
              totalDocuments += 1;
            }
          } catch (error) {
            // If parsing fails, it's a single document
            totalDocuments += 1;
          }
        }
      });

      const totalHours = result.reduce(
        (sum: number, item: any) => sum + parseInt(item.Hours ?? '0', 10),
        0,
      );
      settotalHours(totalHours ?? 0);
      setTotalDocuments(totalDocuments); // You'll need to add this state
    }
  };

  // const listItems = [
  //   {Id: '1', Icon: '', Title: '1'},
  //   {Id: '2', Icon: '', Title: '2'},
  //   {Id: '3', Icon: '', Title: '3'},
  // ];
  // const listTags = [
  //   {Id: '1', Title: '1'},
  //   {Id: '2', Title: '2'},
  //   {Id: '3', Title: '3'},
  // ];

  return (
    <ScrollView
      ref={pros.scrollviewRef}
      nestedScrollEnabled
      {...pros.refreshControlProps}
      style={{
        paddingTop: 16,
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <View
        style={[
          {
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
            alignSelf: 'flex-start',
            width: Dimensions.get('screen').width,
            flex: 1,
            paddingHorizontal: 16,
          },
        ]}>
        <View style={[stylesDefault.mainContainer, {}]}>
          <View
            style={{
              paddingBottom: 8,
              alignSelf: 'flex-start',
            }}>
            <View style={[stylesDefault.mainContent]}>
              {/* infor on top */}
              {pros.check ? (
                <View />
              ) : (
                <>
                  {data?.relativeUser ? (
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        gap: 4,
                        marginBottom: 4,
                      }}>
                      {/* infor img */}
                      <View
                        style={[
                          {
                            width: 20,
                            height: 20,
                            borderRadius: 100,
                            backgroundColor: 'black',
                          },
                        ]}>
                        {data?.relativeUser?.image ? (
                          <FastImage
                            key={data?.relativeUser?.image}
                            source={
                              data?.relativeUser?.image
                                ? {
                                    uri: data?.relativeUser?.image?.includes(
                                      'http',
                                    )
                                      ? data?.relativeUser?.image
                                      : ConfigAPI.getValidLink(
                                          data?.relativeUser?.image,
                                        ),
                                  }
                                : require('../../../assets/appstore.png')
                            }
                            style={{
                              width: '100%',
                              height: '100%',
                              borderRadius: 100,
                            }}
                          />
                        ) : (
                          // show view first charater
                          <View
                            style={{
                              width: '100%',
                              height: '100%',
                              borderRadius: 100,
                              alignItems: 'center',
                              justifyContent: 'center',
                              backgroundColor:
                                ColorThemes.light.Primary_Color_Main,
                            }}>
                            <Text
                              style={{
                                color:
                                  ColorThemes.light
                                    .Neutral_Background_Color_Absolute,
                              }}>
                              {data?.relativeUser?.title
                                ? data?.relativeUser?.title
                                    .charAt(0)
                                    .toUpperCase()
                                : ''}
                            </Text>
                          </View>
                        )}
                      </View>
                      {/* infor text */}
                      <View style={{flex: 1}}>
                        <Text style={[stylesDefault.inforTitle]}>
                          {data?.relativeUser.title ?? ''}
                        </Text>
                      </View>
                    </View>
                  ) : null}
                  {/* title */}
                  {data?.Name ? (
                    <Text
                      style={[
                        stylesDefault.titleStyle,
                        {
                          paddingBottom: data?.Description ? 4 : 0,
                        },
                      ]}
                      numberOfLines={2}>
                      {data?.Name ?? ''}
                    </Text>
                  ) : null}
                  {/* subtitle */}
                  {data?.Description ? (
                    <View style={{paddingTop: 4, paddingBottom: 8, paddingRight: 16, justifyContent: 'flex-start'}}>
                      <Text
                        style={[stylesDefault.subTitleStyle,{textAlign: 'justify'}]}
                        >
                        {data?.Description ?? ''}
                      </Text>
                    </View>
                  ) : null}
                  {listTags?.length ? (
                    <View
                      style={{
                        width: '100%',
                        paddingVertical: 16,
                        flexDirection: 'row',
                        gap: 8,
                        flexWrap: 'wrap',
                      }}>
                      {listTags.map((item, index) => {
                        return (
                          <HashTag
                            key={item.Id}
                            title={item.Name}
                            textStyles={{
                              ...TypoSkin.buttonText6,
                              color:
                                ColorThemes.light.Neutral_Text_Color_Subtitle,
                            }}
                            onPress={() => {
                              navigate(RootScreen.CoursebyTopic, {
                                id: item.Id,
                                title: item.Name,
                              });
                            }}
                            styles={{
                              borderRadius: 24,
                              backgroundColor:
                                ColorThemes.light.Neutral_Background_Color_Main,
                              paddingHorizontal: 8,
                              paddingVertical: 4,
                              margin: 0,
                              alignItems: 'flex-start',
                              height: undefined,
                            }}
                          />
                        );
                      })}
                    </View>
                  ) : null}
                  {/* Rating */}

                  <View
                    style={{
                      gap: 8,
                      borderTopColor:
                        ColorThemes.light.Neutral_Border_Color_Main,
                      borderTopWidth: 1,
                      borderBottomColor:
                        ColorThemes.light.Neutral_Border_Color_Main,
                      borderBottomWidth: 1,
                      paddingVertical: 16,
                    }}>
                    <View
                      style={{
                        width: '100%',
                        flexDirection: 'row',
                        gap: 8,
                        alignItems: 'center',
                      }}>
                      <Rating
                        value={ratings?.averageRating ?? 0}
                        fillColor={ColorThemes.light.Warning_Color_Main}
                        onChange={() => {
                          // if (__DEV__) {
                          //   navigate(RootScreen.LearnCourse);
                          // }
                        }}
                        size={20}
                      />
                      <Text
                        style={{
                          ...TypoSkin.label4,
                          color: ColorThemes.light.Warning_Color_Main,
                        }}>
                        {ratings?.averageRating?.toFixed(1) ?? '0.0'}
                      </Text>
                    </View>
                    <Text
                      style={{
                        ...TypoSkin.label4,
                        color: ColorThemes.light.Warning_Color_Main,
                      }}>
                      {`${
                        ratings?.totalCount > 0
                          ? `${ratings?.totalCount} ${t('ratings')}`
                          : t('noratings')
                      }`}
                    </Text>
                  </View>
                </>
              )}
              <View style={{width: '90%', paddingTop: 16, paddingRight: 16}}>
                <Text
                  style={[
                    TypoSkin.heading7,
                    {
                      color: ColorThemes.light.Neutral_Text_Color_Title,
                      paddingBottom: data?.Description ? 4 : 0,
                    },
                  ]}
                  numberOfLines={2}>
                  {t('thiscourseinclude')}
                </Text>
                <TouchableOpacity
                  onPress={() => {
                    if (countStudent.Customer?.length === 0) return;
                    // showBottomsheet hiển thị danh sách students
                    showBottomSheet({
                      ref: bottomSheetRef,
                      enableDismiss: true,
                      title: 'Học sinh',
                      suffixAction: <View />,
                      prefixAction: (
                        <TouchableOpacity
                          onPress={() => hideBottomSheet(bottomSheetRef)}
                          style={{padding: 6, alignItems: 'center'}}>
                          <Winicon
                            src="outline/layout/xmark"
                            size={20}
                            color={ColorThemes.light.Neutral_Text_Color_Body}
                          />
                        </TouchableOpacity>
                      ),
                      children: (
                        <View
                          style={{
                            height: Dimensions.get('window').height - 300,
                            width: '100%',
                            backgroundColor:
                              ColorThemes.light
                                .Neutral_Background_Color_Absolute,
                          }}>
                          <View
                            style={{
                              flex: 1,
                              backgroundColor:
                                ColorThemes.light
                                  .Neutral_Background_Color_Absolute,
                            }}>
                            <FlatList
                              data={countStudent.Customer}
                              style={{
                                height: '100%',
                                backgroundColor:
                                  ColorThemes.light
                                    .Neutral_Background_Color_Absolute,
                              }}
                              keyExtractor={(_, index) => index.toString()}
                              renderItem={({item}) => {
                                return (
                                  <ListTile
                                    key={item?.Id}
                                    onPress={() => {
                                      hideBottomSheet(bottomSheetRef);
                                      navigate(RootScreen.ProfileCommunity, {
                                        Id: item?.Id,
                                        forEschool: true,
                                      });
                                    }}
                                    listtileStyle={{gap: 8}}
                                    leading={
                                      <SkeletonImage
                                        key={item?.AvatarUrl}
                                        source={{
                                          uri: item?.AvatarUrl
                                            ? `${ConfigAPI.getValidLink(
                                                item?.AvatarUrl,
                                              )}`
                                            : 'https://placehold.co/48/FFFFFF/000000/png',
                                        }}
                                        style={{
                                          width: 48,
                                          height: 48,
                                          borderRadius: 50,
                                          backgroundColor: '#f0f0f0',
                                        }}
                                      />
                                    }
                                    title={
                                      <Text
                                        style={{
                                          ...TypoSkin.heading7,
                                          color:
                                            ColorThemes.light
                                              .Neutral_Text_Color_Title,
                                        }}
                                        numberOfLines={1}>
                                        {item?.Name}
                                      </Text>
                                    }
                                    subTitleStyle={{
                                      ...TypoSkin.subtitle3,
                                      color:
                                        ColorThemes.light
                                          .Neutral_Text_Color_Subtitle,
                                    }}
                                  />
                                );
                              }}
                            />
                          </View>
                        </View>
                      ),
                    });
                  }}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    paddingVertical: 4,
                    gap: 8,
                  }}>
                  <Winicon src={'outline/education/hat-3'} size={16} />
                  <Text style={[stylesDefault.inforTitle, {color: '#313135'}]}>
                    {`${countStudent?.Customer?.length ?? 0} ${t('student')}`}
                  </Text>
                </TouchableOpacity>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    paddingVertical: 4,
                    gap: 8,
                  }}>
                  <Winicon
                    src={'outline/multimedia/video-playlist'}
                    size={16}
                  />
                  <Text style={[stylesDefault.inforTitle, {color: '#313135'}]}>
                    {`${countLesson} ${t('lessons')} (${totalHours} ${t(
                      'hours',
                    )})`}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    paddingVertical: 4,
                    gap: 8,
                  }}>
                  <Winicon src={'outline/education/books'} size={16} />
                  <Text style={[stylesDefault.inforTitle, {color: '#313135'}]}>
                    {`${totalDocuments ?? 0} tài liệu`}
                  </Text>
                </View>
                {data.IsCertificate ? (
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                      paddingVertical: 4,
                      gap: 8,
                    }}>
                    <Winicon
                      src={'outline/user interface/verified'}
                      size={16}
                    />
                    <Text
                      style={[stylesDefault.inforTitle, {color: '#313135'}]}>
                      {'Chứng nhận tốt nghiệp'}
                    </Text>
                  </View>
                ) : (
                  <></>
                )}
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    paddingVertical: 4,
                    gap: 8,
                  }}>
                  <Winicon src={'outline/user interface/g-chart'} size={16} />
                  <Text style={[stylesDefault.inforTitle, {color: '#313135'}]}>
                    {data.Objective}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    paddingVertical: 4,
                    gap: 8,
                  }}>
                  <Winicon src={'outline/buildings/time-alarm'} size={16} />
                  <Text style={[stylesDefault.inforTitle, {color: '#313135'}]}>
                    {data.Time}
                  </Text>
                </View>
              </View>
              {data?.Content ? (
                <View style={{paddingTop: 16}}>
                  <RenderHTML
                    contentWidth={width}
                    source={{html: data?.Content}}
                    tagsStyles={{
                      body: {
                        color: '#313135',
                        fontSize: 14,
                        lineHeight: 20,
                        fontFamily: 'Inter',
                      },
                      strong: {
                        color: '#313135',
                        fontSize: 14,
                        lineHeight: 20,
                        fontFamily: 'Inter',
                        fontWeight: 'bold',
                      },
                      em: {
                        color: '#313135',
                        fontSize: 14,
                        lineHeight: 20,
                        fontFamily: 'Inter',
                      },
                      p: {
                        color: '#313135',
                        fontSize: 14,
                        lineHeight: 20,
                        marginBottom: 10,
                        fontFamily: 'Inter',
                      },
                      h1: {
                        fontSize: 24,
                        fontWeight: 'bold',
                        marginBottom: 16,
                        color: '#18181B',
                        fontFamily: 'Inter',
                      },
                      h2: {
                        fontSize: 20,
                        fontWeight: 'bold',
                        marginBottom: 14,
                        color: '#18181B',
                        fontFamily: 'Inter',
                      },
                      h3: {
                        fontSize: 18,
                        fontWeight: 'bold',
                        marginBottom: 12,
                        color: '#18181B',
                        fontFamily: 'Inter',
                      },
                      ul: {
                        marginLeft: 20,
                        marginBottom: 10,
                      },
                      ol: {
                        marginLeft: 20,
                        marginBottom: 10,
                      },
                      li: {
                        color: '#313135',
                        fontSize: 14,
                        lineHeight: 20,
                        marginBottom: 5,
                        fontFamily: 'Inter',
                      },
                      a: {
                        color: '#0085FF',
                        textDecorationLine: 'underline',
                      },
                      img: {
                        marginVertical: 10,
                        width: '100%',
                        height: 'auto',
                      },
                    }}
                    baseStyle={{
                      color: '#313135',
                      fontSize: 14,
                      lineHeight: 20,
                      fontFamily: 'Inter',
                    }}
                  />
                </View>
              ) : null}
            </View>
          </View>
        </View>
      </View>
      {pros.check ? (
        <></>
      ) : (
        <CourseAslsoLike
          isSeeMore
          horizontal
          titleList={t('youmayaslsolike')}
          id={data?.Id}
          cateId={data?.CategoryId}
        />
      )}
    </ScrollView>
  );
}

const stylesDefault = StyleSheet.create({
  mainContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 16,
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  inforTitle: {
    fontSize: 12,
    color: '#61616B',
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
});
