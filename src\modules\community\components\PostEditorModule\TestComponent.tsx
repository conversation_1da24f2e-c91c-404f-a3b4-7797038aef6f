import React, {useRef} from 'react';
import {View, Text, TouchableOpacity, Alert} from 'react-native';
import RichTextComposer from './index';
import {RichTextComposerRef} from './types';

const TestComponent = () => {
  const editorRef = useRef<RichTextComposerRef>(null);

  const testFunctions = () => {
    const contentWithMentions = editorRef.current?.getContentWithMentions() || '';
    const hashtags = editorRef.current?.getHashtags() || '';
    const videoLinks = editorRef.current?.getVideoLinks() || '';

    Alert.alert(
      'Test Results',
      `Content with mentions: ${contentWithMentions}\n\nHashtags: ${hashtags}\n\nVideo links: ${videoLinks}`,
    );
  };

  return (
    <View style={{flex: 1, padding: 20}}>
      <Text style={{fontSize: 18, fontWeight: 'bold', marginBottom: 20}}>
        Test PostEditor Functions
      </Text>
      
      <Text style={{marginBottom: 10}}>
        Try typing:
        {'\n'}• @username for mentions
        {'\n'}• #hashtag for hashtags  
        {'\n'}• https://youtube.com/watch?v=123 for video links
      </Text>

      <RichTextComposer
        ref={editorRef}
        initialText=""
        onTextChange={() => {}}
        onImagesChange={() => {}}
        onDataChange={() => {}}
      />

      <TouchableOpacity
        style={{
          backgroundColor: '#007AFF',
          padding: 15,
          borderRadius: 8,
          marginTop: 20,
          alignItems: 'center',
        }}
        onPress={testFunctions}>
        <Text style={{color: 'white', fontWeight: 'bold'}}>
          Test Extract Functions
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default TestComponent;
