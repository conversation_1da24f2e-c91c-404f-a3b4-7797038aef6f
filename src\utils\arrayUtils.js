// Hàm shuffle mảng
const shuffleArray = (array) => {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
};

function getRandomElements(arr, count = 5) {
  if (!Array.isArray(arr)) {
    throw new Error('Tham số đầu tiên phải là một mảng');
  }
  
  if (count < 0) {
    throw new Error('Số lượng phần tử phải lớn hơn hoặc bằng 0');
  }
  
  if (count === 0) {
    return [];
  }
  
  if (count >= arr.length) {
    return shuffleArray([...arr]);
  }
  
  const arrCopy = [...arr];
  const result = [];
  
  for (let i = 0; i < count; i++) {
    const randomIndex = Math.floor(Math.random() * arrCopy.length);
    result.push(arrCopy[randomIndex]);
    arrCopy.splice(randomIndex, 1);
  }
  
  return result;
}

export {shuffleArray, getRandomElements};