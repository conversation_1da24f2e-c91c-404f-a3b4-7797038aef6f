import {FlatList, View} from 'react-native';
import {useEffect, useRef, useState} from 'react';
import {
  FBottomSheet,
  ListTile,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {useTranslation} from 'react-i18next';
import {ColorThemes} from '../../../assets/skin/colors';
import {navigate, RootScreen} from '../../../router/router';
import {DataController} from '../../../base/baseController';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import EmptyPage from '../../../Screen/emptyPage';
import ConfigAPI from '../../../Config/ConfigAPI';
import {Ultis} from '../../../utils/Utils';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {CourseDA} from '../da';

export default function Certificates() {
  const [isLoading, setLoading] = useState(false);
  const [isRefresh, setRefresh] = useState(false);
  const {t} = useTranslation();
  const bottomSheetRef = useRef<any>(null);
  const [data, setData] = useState<any>([]);
  const user = useSelectorCustomerState().data;

  const getData = async () => {
    setLoading(true);
    setData([]);
    const cetiController = new DataController('Customer_Certificate');
    const result = await cetiController.getListSimple({
      query: `@CustomerId: {${user?.Id}}`,
    });
    if (result) {
      setData(result.data);
    }
    setLoading(false);
    setRefresh(false);
  };

  useEffect(() => {
    getData();
  }, []);
  const courseDA = new CourseDA();

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FlatList
        nestedScrollEnabled
        refreshing={isRefresh}
        onRefresh={() => {
          setRefresh(true);
          getData();
        }}
        data={data}
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        renderItem={({item, index}) => {
          return (
            <ListTile
              key={index}
              onPress={async () => {
                const result = await courseDA.getCourseDetail(item.CourseId);
                if (result?.data) {
                  const {Id, Name, CertificateName, Author, Signature} =
                    result.data;
                  navigate(RootScreen.Certificate, {
                    courseId: Id,
                    courseName: Name,
                    author: Author,
                    signature: Signature,
                    CerticateNo:
                      item.CerticateNo ??
                      'CERT-' + Math.floor(Math.random() * 1000000),
                    customerName: item?.CustomerName ?? user.Name,
                    dateCompleted: item
                      ? new Date(item?.DateCreated ?? 0).toLocaleDateString(
                          'vi-VN',
                        )
                      : new Date().toLocaleDateString('vi-VN'),
                    isCertificate: item ? true : false,
                    certificateName: CertificateName,
                  });
                }
              }}
              style={{
                borderColor: ColorThemes.light.Neutral_Border_Color_Main,
                borderWidth: 1,
                marginHorizontal: 16,
                padding: 0,
              }}
              listtileStyle={{
                padding: 16,
              }}
              leading={
                <View
                  style={{
                    width: 56,
                    height: 56,
                    borderRadius: 100,
                    overflow: 'hidden',
                  }}>
                  <SkeletonImage
                    key={item?.CertificateImage}
                    source={{
                      uri: item?.CertificateImage
                        ? `${ConfigAPI.getValidLink(item?.CertificateImage)}`
                        : 'https://placehold.co/56/FFFFFF/000000/png',
                    }}
                    style={{
                      width: '100%',
                      height: '100%',
                      borderRadius: 100,
                      objectFit: 'cover',
                    }}
                    resizeMode="cover"
                  />
                </View>
              }
              title={item?.Name ?? ''}
              subtitle={Ultis.datetoString(
                new Date(item?.DateCreated ?? 0),
                'dd/MM/yyyy hh:mm',
              )}
              trailing={
                <Winicon
                  src="outline/user interface/view"
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                  size={16}
                />
              }
            />
          );
        }}
        style={{width: '100%', height: '100%', paddingTop: 32}}
        // keyExtractor={a => a.Id?.toString()}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={() => {
          if (isLoading) {
            return (
              <View style={{gap: 8, paddingTop: 16}}>
                {[1, 2, 3].map((_, index) => (
                  <SkeletonCertificateCard key={`skeleton-${index}`} />
                ))}
              </View>
            );
          }
          return <EmptyPage title={t('nodata')} />;
        }}
      />
    </View>
  );
}

const SkeletonCertificateCard = () => {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View style={{marginHorizontal: 16, marginBottom: 16}}>
        {/* Certificate card skeleton */}
        <View
          style={{
            borderRadius: 8,
            borderWidth: 1,
            borderColor: '#e0e0e0',
            padding: 16,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          {/* Title and date container */}
          <View style={{flex: 1, gap: 8}}>
            {/* Title placeholder */}
            <View
              style={{
                width: '70%',
                height: 18,
                borderRadius: 4,
              }}
            />
            {/* Date placeholder */}
            <View
              style={{
                width: '50%',
                height: 14,
                borderRadius: 4,
              }}
            />
          </View>

          {/* Icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 12,
            }}
          />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};
