import {useState, useCallback} from 'react';
import {Alert, Platform} from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import {ImageItem} from '../types';
import ConfigAPI from '../../../../../Config/ConfigAPI';

export const useImagePicker = (
  initialImages: ImageItem[] = [],
  initialImageIds: string[] = [],
  maxImages: number = 10,
) => {
  // Khởi tạo selectedImages từ initialImages hoặc từ initialImageIds
  const [selectedImages, setSelectedImages] = useState<ImageItem[]>(() => {
    if (initialImages.length > 0) {
      return initialImages;
    }

    // Chuyển đổi initialImageIds thành ImageItem[]
    if (initialImageIds.length > 0) {
      return initialImageIds.map((id, index) => ({
        id: `existing-${index}`,
        uri: ConfigAPI.getValidLink(id),
        path: ConfigAPI.getValidLink(id),
        mime: 'image/jpeg', // <PERSON><PERSON><PERSON> định mime type
        filename: `image-${index}.jpg`,
        // Thêm trường để đánh dấu đây là ảnh đã tồn tại
        existingId: id,
      }));
    }

    return [];
  });

  // Hàm chọn nhiều ảnh
  const handlePickImages = useCallback(async () => {
    try {
      if (selectedImages.length >= maxImages) {
        Alert.alert(
          'Thông báo',
          `Bạn chỉ có thể chọn tối đa ${maxImages} ảnh.`,
        );
        return;
      }

      const remainingSlots = maxImages - selectedImages.length;

      const images = await ImagePicker.openPicker({
        multiple: true,
        mediaType: 'photo',
        maxFiles: remainingSlots,
        compressImageQuality: 0.8,
      });

      const newImages = images.map(img => ({
        id: Math.random().toString(36).substring(2, 15),
        uri: Platform.OS === 'ios' ? img.sourceURL || img.path : img.path,
        path: img.path,
        mime: img.mime,
        filename: img.filename || `image-${Date.now()}.jpg`,
        width: img.width,
        height: img.height,
      }));

      setSelectedImages(prev => [...prev, ...newImages]);
    } catch (error) {
      if (
        error instanceof Error &&
        error.message !== 'User cancelled image selection'
      ) {
        console.error('Error picking images:', error);
        Alert.alert('Lỗi', 'Không thể chọn ảnh. Vui lòng thử lại.');
      }
    }
  }, [selectedImages, maxImages]);

  // Hàm xóa ảnh
  const handleRemoveImage = useCallback((imageId: string) => {
    Alert.alert(
      'Xác nhận',
      'Bạn có chắc muốn xóa ảnh này?',
      [
        {
          text: 'Hủy',
          style: 'cancel',
        },
        {
          text: 'Xóa',
          onPress: () => {
            setSelectedImages(prev => prev.filter(img => img.id !== imageId));
          },
          style: 'destructive',
        },
      ],
      {cancelable: true},
    );
  }, []);

  // Hàm xóa tất cả ảnh
  const handleRemoveAllImages = useCallback(() => {
    setSelectedImages([]);
  }, []);

  return {
    selectedImages,
    setSelectedImages,
    handlePickImages,
    handleRemoveImage,
    handleRemoveAllImages,
  };
};
