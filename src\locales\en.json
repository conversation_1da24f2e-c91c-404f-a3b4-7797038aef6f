{"welcome": "Welcome to our app!", "change_language": "Change Language", "japanese_ad": "Japanese advanced", "japanese_bs": "Japanese Basic", "Popular_courses": "Popular courses", "Recent_course": "Recent course", "student": "Students", "bestInstructor": "Best instructor", "specialList": "Learn with specialist", "social": "Social", "thiscourseinclude": "This course include", "lessons": "lesson", "hours": "hours", "youmayaslsolike": "You may also like", "ratings": "ratings", "noratings": "No reviews yet", "following": "Following", "followed": "Followed", "follow": "Follow", "nodata": "No data", "common": {"back": "Back", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "search": "Search", "create": "Create", "description": "Description", "noData": "No data", "loading": "Loading", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "next": "Next", "previous": "Previous", "continue": "Continue", "finish": "Finish", "retry": "Retry", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "seeMore": "See more", "seeLess": "See less", "all": "All", "more": "More", "less": "Less", "filter": "Filter", "sort": "Sort", "apply": "Apply", "reset": "Reset", "clear": "Clear", "done": "Done", "notFound": "Not found", "titleBottom": "Title", "noMoreData": "No more data", "add": "Add", "refresh": "Refresh", "addMore": "Add more", "addNew": "Add new", "deleteAll": "Delete all", "confirmDelete": "Are you sure you want to delete this product?", "confirmDeleteAll": "Are you sure you want to delete all?", "total": "Total", "list": "List", "productName": "Product name", "quantity": "Quantity", "enterDescription": "Please enter description", "enterQuantity": "Please enter quantity", "failed": "Failed"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "fullName": "Full Name", "phoneNumber": "Phone Number", "rememberMe": "Remember Me", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "signUp": "Sign Up", "signIn": "Sign In", "enterEmail": "Enter your email", "enterPassword": "Enter your password", "enterFullName": "Enter your full name", "enterPhoneNumber": "Enter your phone number", "passwordNotMatch": "Passwords do not match", "passwordRequirements": "Password must be at least 8 characters", "forgotPasswordDescription": "Enter your email and we'll send you a link to reset your password", "resetPasswordSuccess": "Your password has been reset successfully", "loginSuccess": "Login successful", "loginFailed": "<PERSON><PERSON> failed", "registerSuccess": "Registration successful", "registerFailed": "Registration failed", "logoutConfirm": "Are you sure you want to logout?", "logoutSuccess": "Logout successful"}, "course": {"popular": "Popular courses", "recent": "Recent courses", "detail": "Course Details", "lessons": "Lessons", "hours": "Hours", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "instructors": "Instructors", "rating": "Rating", "ratings": "Ratings", "reviews": "Reviews", "noReviews": "No reviews yet", "price": "Price", "free": "Free", "enroll": "Enroll", "enrolled": "Enrolled", "continue": "Continue Learning", "start": "Start Learning", "complete": "Complete", "completed": "Completed", "inProgress": "In Progress", "notStarted": "Not Started", "certificate": "Certificate", "duration": "Duration: {{hours}} hours", "objective": "Objective", "requirements": "Requirements", "description": "Description", "content": "Content", "curriculum": "Curriculum", "whatYouWillLearn": "What You Will Learn", "relatedCourses": "Related Courses", "youMayAlsoLike": "You may also like", "noTitle": "No title", "noDescription": "No description", "noContent": "No content", "courseInclude": "This course includes", "totalLessons": "{{count}} lessons", "totalHours": "{{count}} hours", "bestInstructors": "Best instructors", "specialistList": "Learn with specialists", "japaneseAdvanced": "Japanese advanced", "japaneseBasic": "Japanese basic", "search": "Search courses", "undefinedCourse": "Undefined course", "durationMinutes": "Duration: {{minutes}} minutes", "overview": "Overview", "learn": "Learn", "buyNow": "Buy Now", "total": "Total", "loadingTimeout": "Loading timeout. Please try again.", "videoList": "Video List", "loadingVideos": "Loading videos...", "noVideos": "No videos", "noVideosDescription": "This lesson has no videos yet", "noLessonsFound": "No lessons found", "failedToProcessLessonData": "Failed to process lesson data", "invalidLessonsData": "Invalid lessons data", "notCompletedYet": "You have not completed this course yet!", "lesson": "Lesson {{step<PERSON>rde<PERSON>}}: {{lessonName}}", "lessonNotFound": "Lesson information not found", "cannotLoadLesson": "Cannot load lesson data", "loadingError": "Error occurred while loading data", "noVideo": "No video", "loadingDocument": "Loading document...", "noDocument": "No documents", "documentNotSupported": "This document does not support preview", "currentlyViewing": "Currently viewing this document - Tap to view again", "tapToView": "Tap to view document", "documentNumber": "Document {{number}}", "pdfDocument": "PDF Document", "introduction": "Introduction", "vocabulary": "Vocabulary", "review": "Review"}, "game": {"altp": {"title": "Who Wants to Be a Millionaire", "welcomeTo": "Welcome to", "start": "Start", "ranking": "Ranking", "help": "Help", "helpDescription": "Choose a stage to start playing. Answer questions correctly to unlock the next stages.", "expertHelp": "Ask the Expert", "phoneCall": "Call a Friend", "audienceHelp": "Ask the Audience", "correctAnswer": "Correct Answer", "wrongAnswer": "Wrong Answer", "timeUp": "Time's Up", "gameOver": "Game Over", "winner": "Winner", "quit": "Quit", "continue": "Continue", "nextQuestion": "Next Question", "finalAnswer": "Final Answer", "milestone": "Milestone", "stage": "Stage", "stageLockedMessage": "You need to complete the previous stages to unlock this stage.", "gameDescription": "Answer 15 questions\nwith 4 lifelines", "score": "Score", "highScore": "High Score", "newHighScore": "New High Score", "totalScore": "Total Score", "playerName": "Player Name", "date": "Date", "time": "Time", "expertOpinion": "According to my analysis, the correct answer is {{answer}}.", "friendOpinion": "Your friend thinks the correct answer is {{answer}}.", "audienceOpinion": "Audience Poll Results"}}, "profile": {"title": "Profile", "editInfo": "Edit Information", "settings": "Settings", "personalInfo": "Personal Information", "accountSettings": "Account <PERSON><PERSON>", "notifications": "Notifications", "security": "Security", "privacy": "Privacy", "language": "Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "systemDefault": "System Default", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "bio": "Bio", "avatar": "Avatar", "changeAvatar": "Change Avatar", "removeAvatar": "Remove Avatar", "saveChanges": "Save Changes", "cancelChanges": "Cancel Changes", "deleteAccount": "Delete Account", "deleteAccountConfirm": "Are you sure you want to delete your account?", "deleteAccountWarning": "All data will be deleted and cannot be recovered.", "accountDeleted": "Your account has been deleted successfully", "faq": "Frequently Asked Questions", "faqCategory": "FAQ Categories", "faqCategoryLabel": "Question categories", "birthdate": "Date of Birth", "gender": "Gender", "male": "Male", "female": "Female", "rank": "Rank", "rankPrefix": "Rank", "rankNormal": "Normal", "rankVIP": "VIP", "accountCreationDate": "Account Creation Date", "createPassword": "Create Password", "updatePassword": "Update Password", "selectBirthdate": "Select birthdate", "invalidPhone": "Invalid phone number", "phoneRequired": "Phone number is required", "account": "Account", "touchFaceId": "Touch ID/Face ID", "purchaseHistory": "Purchase History", "savedVideos": "Saved Videos", "policy": "Policy", "logout": "Logout", "login": "<PERSON><PERSON>", "yourPoints": "Your Sakupi", "points": "<PERSON><PERSON><PERSON>", "updateAvatarSuccess": "Avatar updated successfully", "newFileImg": "new file img", "normal": "Normal", "vip": "VIP"}, "purchase": {"history": "Purchase History", "details": "Purchase Details", "date": "Purchase Date", "amount": "Amount", "status": "Status", "orderNumber": "Order Number", "paymentMethod": "Payment Method", "billingInfo": "Billing Information", "noOrders": "No orders found", "orderStatus": {"pending": "Pending", "processing": "Processing", "completed": "Completed", "cancelled": "Cancelled", "refunded": "Refunded", "failed": "Failed"}, "receipt": "Receipt", "invoice": "Invoice", "downloadInvoice": "Download Invoice", "viewDetails": "View Details"}, "community": {"posts": "Posts", "comments": "Comments", "likes": "<PERSON>s", "share": "Share", "report": "Report", "follow": "Follow", "following": "Following", "followers": "Followers", "groups": "Groups", "createGroup": "Create Group", "joinGroup": "Join Group", "leaveGroup": "Leave Group", "members": "Members", "admin": "Admin", "moderator": "Moderator", "member": "Member", "post": "Post", "createPost": "Create Post", "editPost": "Edit Post", "deletePost": "Delete Post", "comment": "Comment", "reply": "Reply", "writeComment": "Write a comment...", "writePost": "What's on your mind?", "postPlaceholder": "Share your thoughts...", "addPhoto": "Add Photo", "addVideo": "Add Video", "trendingnew": "Trending news", "latest": "Latest", "popular": "Popular", "noPostsYet": "No posts yet", "beTheFirstToPost": "Be the first to post!", "reportReason": "Reason for reporting", "reportSubmitted": "Report submitted", "blockUser": "Block User", "unblockUser": "Unblock User", "blockedUsers": "Blocked Users", "activities": "Community\nActivities", "featureInDevelopment": "Feature in development", "reportPost": "Report post", "actions": "Actions", "confirm": "Confirm", "moderation": "Moderation", "groupName": "Group Name", "groupImage": "Group Image", "tabs": {"home": "Home", "explore": "Explore", "group": "Group", "chat": "Cha<PERSON>", "notify": "Notify", "popular": "Popular", "forYou": "For you", "following": "Following", "bookmark": "Bookmark"}}, "exam": {"sections": "Sections", "testHistory": "Test History", "pass": "Pass", "fail": "Fail", "totalQuestions": "Total questions: {{count}}", "timeLimit": "Time limit: {{minutes}} minutes", "requirements": "Requirements", "passingRequirement": "You need to answer correctly {{percent}} points to pass the exam.", "results": "Results: {{count}} attempts", "attempt": "Attempt {{number}}", "noResults": "No results yet", "startExam": "Start Exam", "submit": "Submit", "confirmSubmit": "Do you want to submit?", "confirmExit": "Do you want to exit?", "exitWarning": "If you exit, your results will not be saved!", "selectQuestion": "Select question", "selectOneAnswer": "Select one answer", "selectMultipleAnswers": "Select multiple answers", "previousQuestion": "Previous", "nextQuestion": "Next", "exit": "Exit", "continueExam": "Continue <PERSON>am", "submitNow": "Submit Now", "multiple": "multiple", "resultsTitle": "Results", "noAnswers": "You haven't answered any questions", "answered": "Answered", "congratulations": "Congratulations! You have completed the exam", "tryAgainWhenReady": "Try again when you're ready", "finish": "Finish", "retake": "Retake", "retakeExam": "Retake Exam", "confirmRetake": "Do you want to retake the exam?", "exam": "Exam", "question": "Question", "previousExam": "Previous Exam", "nextExam": "Next Exam", "correct": "Corrected", "incorrect": "Incorrected", "unanswered": "Unanswered", "deleteExam": "Delete this exam?", "correctAnswersCount": "{{correct}}/{{total}} correct answers", "timeMinutes": "{{time}} minutes", "passed": "Passed", "failed": "Failed", "submitSection": "Submit Section", "listenToQuestion": "Listen to Question", "examDocument": "Exam Document"}, "notification": {"title": "Notifications", "all": "All", "unread": "Unread", "readAll": "Read all", "markAllAsRead": "All notifications marked as read", "noNotifications": "You have no notifications", "postNotExist": "Post does not exist", "daysAgo": "{{count}} days ago", "hoursAgo": "{{count}} hours ago", "minutesAgo": "{{count}} minutes ago", "reject": "Reject", "accept": "Accept"}, "app": {"name": "ITM", "slogan": "いらっしゃいませ", "version": "Version", "about": "About", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "contactUs": "Contact Us", "feedback": "<PERSON><PERSON><PERSON>", "rateApp": "Rate App", "shareApp": "Share App", "update": "Update", "newVersionAvailable": "New version available", "updateNow": "Update Now", "later": "Later", "loading": "Loading", "error": "Error", "retry": "Retry", "offline": "You are offline", "checkConnection": "Please check your internet connection", "reconnect": "Reconnect", "sessionExpired": "Session expired", "loginAgain": "Please login again", "maintenance": "Maintenance", "maintenanceMessage": "The app is currently under maintenance. Please try again later."}, "video": {"savedVideos": "Saved Videos", "noSavedVideos": "No saved videos", "noSavedVideosSubtitle": "You can download videos from learning screen to watch offline", "loadingVideos": "Loading video list...", "deleteVideo": "Delete video", "deleteVideoConfirm": "Are you sure you want to delete video \"{{videoName}}\"?", "deleteAllVideos": "Delete all videos", "deleteAllVideosConfirm": "Are you sure you want to delete all videos of lesson \"{{lessonName}}\"?", "deleteAllSavedVideos": "Delete all saved videos", "deleteAllSavedVideosConfirm": "Are you sure you want to delete all saved videos?", "deleteVideoSuccess": "Video deleted successfully", "deleteAllVideosSuccess": "All videos deleted successfully", "deleteVideoFailed": "Cannot delete video", "deleteVideoError": "Error occurred while deleting video", "loadVideosFailed": "Cannot load saved videos list", "videoCount": "{{count}} videos", "totalSize": "{{size}}"}, "flashcard": {"publicFlashCards": "Popular FlashCards", "myFlashCards": "My FlashCards", "defaultUser": "User", "wordsCount": "{{count}} words", "learnersCount": "{{count}} learners", "deleteSuccess": "FlashCard deleted successfully", "deleteError": "Error occurred while deleting FlashCard", "searchPlaceholder": "Search FlashCards...", "selectTopic": "Select topic", "allTopics": "All topics", "unknownTopic": "Unknown topic", "topicsSelected": "topics"}}