import {
  SafeAreaView,
  View,
  ActivityIndicator,
  Text,
  StyleSheet,
} from 'react-native';

const LoadingGame = () => {
  <SafeAreaView style={styles.safeArea}>
    <View style={[styles.container, styles.centerContent]}>
      <ActivityIndicator size="large" color="#112164" />
      <Text style={styles.loadingText}><PERSON><PERSON> tải câu hỏi...</Text>
    </View>
  </SafeAreaView>;
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    padding: 16,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#112164',
    marginTop: 16,
  },
});

export default LoadingGame;
