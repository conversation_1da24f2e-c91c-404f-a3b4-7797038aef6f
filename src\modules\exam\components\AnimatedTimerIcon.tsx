import React, {useEffect, useRef} from 'react';
import {Animated, ViewStyle} from 'react-native';
import FastImage, {ImageStyle} from '@d11/react-native-fast-image';

interface AnimatedTimerIconProps {
  shouldRotate: boolean;
  style?: ViewStyle;
  imgStyle?: ImageStyle;
  source: any;
}

const AnimatedTimerIcon: React.FC<AnimatedTimerIconProps> = ({
  shouldRotate,
  style,
  imgStyle,
  source,
}) => {
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (shouldRotate) {
      // Animate rotation from 0 to 180 degrees (upside down)
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 1000, // 1 second animation
        useNativeDriver: true,
      }).start();
    }
  }, [shouldRotate, rotateAnim]);

  const rotateInterpolate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{rotate: rotateInterpolate}],
        },
      ]}>
      <FastImage source={source} style={imgStyle} />
    </Animated.View>
  );
};

export default AnimatedTimerIcon;
