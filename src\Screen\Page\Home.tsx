/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Animated,
  Dimensions,
  Image,
  Linking,
  Pressable,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {
  AppButton,
  AppSvg,
  FBottomSheet,
  showBottomSheet,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../router/router';
import CourseRecent from '../../modules/Course/listview/recent';
import BestInstructor from '../../modules/customer/listview/bestInstructor';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import TitleWithImage from '../Layout/titleWithImage';
import CoursePopular from '../../modules/Course/listview/Popurlar';
import {SearchIndex} from '../../modules/Course/listview/search';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {ScrollView} from 'react-native-gesture-handler';
import {CustomerRankType} from '../../Config/Contanst';
import JapanAdvance from '../../modules/Course/listview/japanAdvance';
import JapanBasic from '../../modules/Course/listview/japanBasic';
import SpecialList from '../../modules/Course/listview/specialList';
import {DataController} from '../../base/baseController';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from '@d11/react-native-fast-image';
import {useNetworkStatus} from '../../hooks/useNetworkStatus';
import OfflineMessage from '../../components/OfflineMessage';
import { IconSvg } from '../../components/iconSvg';

export const LogoImg = () => {
  return (
    <AppSvg SvgSrc={IconSvg.dash} size={24} style={{alignItems: 'center', justifyContent: 'center'}} />
  );
};

export const ProfileEschoolView = (props: any) => {
  const user = useSelectorCustomerState().data;

  return (
    <View
      pointerEvents={props.onlyView ? 'none' : 'auto'}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
      }}>
      <TouchableOpacity
        onPress={
          props.onlyView
            ? undefined
            : () => {
                navigate(RootScreen.navigateESchoolView, {
                  screen: 'Profile',
                });
              }
        }>
        {user?.AvatarUrl ? (
          <FastImage
            key={user?.AvatarUrl}
            source={
              user?.AvatarUrl
                ? {
                    uri: user?.AvatarUrl.includes('https')
                      ? user?.AvatarUrl
                      : ConfigAPI.getValidLink(user?.AvatarUrl),
                  }
                : require('../../assets/appstore.png')
            }
            style={{
              width: props.size ? props.size : 32,
              height: props.size ? props.size : 32,
              borderRadius: 50,
              backgroundColor: '#f0f0f0',
              borderColor: ColorThemes.light.Neutral_Border_Color_Main,
              borderWidth: 1,
            }}
          />
        ) : (
          <View
            style={{
              width: props.size ? props.size : 32,
              height: props.size ? props.size : 32,
              borderRadius: 50,
              backgroundColor: ColorThemes.light.Primary_Color_Main,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text
              style={{
                ...TypoSkin.heading7,
                color: ColorThemes.light.Neutral_Background_Color_Absolute,
              }}>
              {user?.Name
                ? user.Name.charAt(0).toUpperCase()
                : user?.Email
                ? user.Email.charAt(0).toUpperCase()
                : ''}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

const Home = () => {
  const navigation = useNavigation<any>();
  const bottomSheetRef = useRef<any>(null);
  const {t} = useTranslation();
  // const { changeLanguage } = useLanguage();
  const customer = useSelectorCustomerState().data;
  const bannerDA = new DataController('Banner');
  const [banners, setBanners] = useState<Array<any>>([]);
  const [isLoading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(0)).current; // Initial opacity value
  //network status
  const networkStatus = useNetworkStatus();

  // Function to start the fade-in animation
  const fadeIn = () => {
    // Reset opacity to 0
    fadeAnim.setValue(0);

    // Start the fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000, // 1 second fade-in
      useNativeDriver: true,
    }).start();
  };

  const getBanner = async () => {
    setLoading(true);
    try {
      const result = await bannerDA.getAll();
      if (result) {
        setBanners(result.data);
        // Start fade animation after data is loaded
        fadeIn();
      }
    } catch (error) {
      console.error('Failed to fetch banners:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // State to trigger refreshes in child components
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleRefresh = () => {
    setRefreshing(true);

    // Trigger refresh in child components by updating the refreshTrigger
    // This will cause the useEffect to run and call getBanner()
    setRefreshTrigger(prev => prev + 1);
  };

  // Effect to get banner data on initial load and when refreshTrigger changes
  useEffect(() => {
    getBanner();
  }, [refreshTrigger]);

  // Start fade animation when component mounts
  useEffect(() => {
    fadeIn();
  }, []);

  return (
    <TitleWithImage
      prefix={
        <TouchableOpacity
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
          style={{padding: 4}}>
            
            
          <LogoImg />
        </TouchableOpacity>
      }
      title={customer?.Name ?? customer?.Email ?? 'Người dùng'}
      subTitle={
        customer?.RankInfor
          ? `Hạng ${customer?.RankInfor?.Name ?? ''}`
          : undefined
      }
      iconActionPress={() => {
        navigate(RootScreen.Notification);
      }}
      action={
        <AppButton
          backgroundColor={'transparent'}
          borderColor="transparent"
          onPress={() => {
            showBottomSheet({
              ref: bottomSheetRef,
              enableDismiss: true,
              children: <SearchIndex ref={bottomSheetRef} />,
            });
          }}
          containerStyle={{
            borderRadius: 100,
            padding: 6,
            height: 32,
            width: 32,
            backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
          }}
          title={
            <Winicon
              src={'outline/user interface/search'}
              size={18}
              color={ColorThemes.light.Neutral_Text_Color_Title}
            />
          }
        />
      }>
      <FBottomSheet ref={bottomSheetRef} />
      {/* Main content */}
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.Primary_Color_Main]}
            tintColor={ColorThemes.light.Primary_Color_Main}
          />
        }>
        {!networkStatus.isConnected ? (
          <OfflineMessage show={true} />
        ) : (
          <>
            <TouchableOpacity
              onPress={() => {
                // check convert undefined thì return
                // if (!banners[activeIndex]) return;
                // const link = banners[activeIndex]?.LinkApp;
                // if (link) {
                //   // check link co Course thi navigate : "Course?id=41e2e36d982547b8adf7483682692407"
                //   if (link.includes('Course?id=')) {
                //     const id = link.split('Course?id=')[1];
                //     navigate(RootScreen.CourseDetail, {id});
                //     return;
                //   }
                // } else {
                //   Linking.openURL(banners[activeIndex]?.Link).catch(err => {
                //     console.error('Error downloading document:', err);
                //   });
                // }
              }}
              style={{
                marginBottom: 32,
                borderRadius: 8,
                backgroundColor: '#fff',
                width: '100%',
                height: 220,
                overflow: 'hidden',
                justifyContent: 'center',
                alignItems: 'center',
                // padding: 16,
              }}>
              {/* Learn now button removed as it's now part of each slide */}
              {/* <View
                style={{
                  position: 'absolute',
                  bottom: 16,
                  left: 16,
                  zIndex: 99,
                }}>
                <AppButton
                  title={'Xen thêm'}
                  containerStyle={{
                    justifyContent: 'flex-start',
                    alignSelf: 'baseline',
                  }}
                  backgroundColor={'transparent'}
                  textStyle={{
                    ...TypoSkin.buttonText3,
                    color: ColorThemes.light.Neutral_Background_Color_Absolute,
                  }}
                  borderColor="transparent"
                  suffixIconSize={16}
                  suffixIcon={'outline/arrows/circle-arrow-right'}
                  onPress={() => {
                    const link = banners[activeIndex]?.LinkApp;
                    if (link) {
                      // check link co Course thi navigate : "Course?id=41e2e36d982547b8adf7483682692407"
                      if (link.includes('Course?id=')) {
                        const id = link.split('Course?id=')[1];
                        navigate(RootScreen.CourseDetail, {id});
                        return;
                      }
                    } else {
                      if(!banners[activeIndex]?.Link || banners[activeIndex]?.Link === '' || banners[activeIndex]?.Link === null) return;
                      Linking.openURL(banners[activeIndex]?.Link).catch(err => {
                        console.error('Error downloading document:', err);
                      });
                    }
                  }}
                  textColor={
                    ColorThemes.light.Neutral_Background_Color_Absolute
                  }
                />
              </View> */}
              <SwiperFlatList
                autoplay
                autoplayDelay={7}
                autoplayLoop
                showPagination={false}
                pagingEnabled
                style={{width: Dimensions.get('window').width, height: '100%', backgroundColor: '#fff'}}
                data={banners}
                onChangeIndex={({index}) => {
                  // Update active index and trigger fade animation
                  setActiveIndex(index);
                  fadeIn();
                }}
                renderItem={({item}) => (
                  <View
                    key={item.Img}
                    style={{
                      width: Dimensions.get('window').width,
                      height: '100%',
                    }}>
                    {/* thêm một lớp đổ bóng ghi đè hình ảnh từ dưới lên và mờ dần để hiển thị chữ rõ ràng hơn */}
                    {/* <LinearGradient
                      colors={['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0.3)']}
                      style={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        height: '60%',
                        zIndex: 98,
                      }}
                    /> */}

                    {/* <View
                      style={{
                        position: 'absolute',
                        bottom: 60,
                        left: 16,
                        zIndex: 99,
                      }}>
                      <Animated.Text
                        style={{
                          ...TypoSkin.heading6,
                          color:
                            ColorThemes.light.Neutral_Background_Color_Absolute,
                          opacity: fadeAnim, // Bind opacity to animated value
                        }}>
                        {item.Name}
                      </Animated.Text>
                    </View> */}
                    <FastImage
                      key={item.Img}
                      onError={() => {}}
                      source={{uri: ConfigAPI.getValidLink(item.Img)}}
                      resizeMode='contain'

                        style={{
                          width: '100%',
                          height: '100%',
                          backgroundColor: '#fff',
                        }}

                    />
                  </View>
                )}
              />
            </TouchableOpacity>
            {/* <DefaultPageView titleList={t('Popular_courses')} /> */}
            <JapanBasic
              horizontal={true}
              titleList={t('japanese_bs')}
              id="3aba11bce83144deb9749973b8751142"
              isSeeMore={true}
              onRefresh={() => {
                // This will be called when the main refresh happens
                console.log('Refreshing JapanBasic');
              }}
              onPressSeeMore={() => {
                navigation.push(RootScreen.listCoursebyCate, {
                  id: '3aba11bce83144deb9749973b8751142',
                  title: `${t('japanese_bs')}`,
                });
              }}
              key={`japan-basic-${refreshTrigger}`} // Force re-render on refresh
            />
            <CoursePopular
              titleList={t('Popular_courses')}
              key={`popular-${refreshTrigger}`}
            />
            {/* <CourseRecent
              titleList={t('Recent_course')}
              key={`recent-${refreshTrigger}`}
            /> */}
            <JapanAdvance
              horizontal={true}
              titleList={t('japanese_ad')}
              id="40caf5c67af14eb8b0c6de945d1d6f93"
              isSeeMore={true}
              onRefresh={() => {
                // This will be called when the main refresh happens
                console.log('Refreshing JapanAdvance');
              }}
              onPressSeeMore={() => {
                navigation.push(RootScreen.listCoursebyCate, {
                  id: '40caf5c67af14eb8b0c6de945d1d6f93',
                  title: `${t('japanese_ad')}`,
                });
              }}
              key={`japan-advance-${refreshTrigger}`} // Force re-render on refresh
            />
            
            <Pressable style={{height: 24}} />
            <BestInstructor
              horizontal
              titleList={t('bestInstructor')}
              isSeeMore
              key={`best-instructor-${refreshTrigger}`}
            />
            <SpecialList
              horizontal={true}
              titleList={t('specialList')}
              id="a54c77d99d3a4a47a178e4155304d74d"
              key={`special-list-${refreshTrigger}`}
              onPressSeeMore={() => {
                navigation.push(RootScreen.listCoursebyCate, {
                  id: 'a54c77d99d3a4a47a178e4155304d74d',
                  title: `${t('specialList')}`,
                });
              }}
            />
          </>
        )}

        {/* banner */}
      </ScrollView>
    </TitleWithImage>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    width: '100%',
  },
  titleText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  textContainer: {
    paddingRight: 10,
  },
  mainText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
});

export default Home;
