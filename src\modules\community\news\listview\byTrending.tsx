/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-hooks/exhaustive-deps */
import React, {forwardRef, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Pressable,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {
  AppButton,
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import {DefaultNew} from '../card/defaultNew';
import ScreenHeader from '../../../../Screen/Layout/header';
import {TextFieldForm} from '../form/component-form';
import {useForm} from 'react-hook-form';
import {useNavigation, useRoute} from '@react-navigation/native';
import {RootScreen} from '../../../../router/router';
import {onShare} from '../../../../features/share';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../../redux/store/store';
import {useNewsFeedData} from '../../hook/newsFeedHook';
import {
  fetchComments,
  fetchCustomers,
  fetchLikes,
  getPostsForUser,
  newsFeedActions,
} from '../../reducers/newsFeedReducer';
import CommentsListNews from '../../../customer/listview/commentsNews';
import {getDataToAsyncStorage} from '../../../../utils/AsyncStorage';
import {StorageContanst} from '../../../../Config/Contanst';
import {dialogCheckAcc} from '../../../../Screen/Layout/mainLayout';
import EmptyPage from '../../../../Screen/emptyPage';
import {DataController} from '../../../../base/baseController';
import {useSelectorCustomerState} from '../../../../redux/hook/customerHook';
import {randomGID, Ultis} from '../../../../utils/Utils';
import ConfigAPI from '../../../../Config/ConfigAPI';
interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
  isRefresh?: boolean;
  setRefresh?: (value: boolean) => void;
}
export default function ByNewTrending(props: Props) {
  // const [data, setData] = useState<Array<any>>([]);

  const navigation = useNavigation<any>();
  const popupRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const dispatch = useDispatch<any>();
  const route = useRoute();
  const [page, setPage] = useState(1);
  const size = 100;
  const [data, setData] = useState<Array<any>>([]);
  const [tag, setTag] = useState<Array<any>>([]);
  const [loading, setLoading] = useState(true);
  const news = new DataController('News');
  const cusId = useSelectorCustomerState().data?.Id;
  useEffect(() => {
    getData();
  }, []);
  const getData = async () => {
    setLoading(true);
    if (props.setRefresh) {
      props.setRefresh(true);
    }
    const result = await news.getListSimple({
      page: page,
      size: size,
      query: '@IsHot:{true}',
    });

    if (result) {
      var ids = result.data.map((item: any) => item.Id);
      // lọc ids trùng nhau
      ids = [...new Set(ids)];
      var cusIds = result.data.map((item: any) => item.CustomerId);
      cusIds = [...new Set(cusIds)];
      // Fetch all data in parallel
      const [customers, likes, comments] = await Promise.all([
        fetchCustomers(cusIds),
        fetchLikes(ids, true),
        fetchComments(ids, true),
      ]);

      // Enrich posts with fetched data
      const data = result.data.map((post: any) => {
        const customer = customers.find((c: any) => c.Id === post.CustomerId);
        const postLikes = likes.filter((l: any) => l.NewsId === post.Id);
        const commentCount =
          comments.find((c: any) => c.NewsId === post.Id)?.CommentsCount || 0;

        return {
          ...post,
          Likes: postLikes,
          IsLike: cusId
            ? postLikes.some((like: any) => like.CustomerId === cusId)
            : false,
          Comment: commentCount,
          relativeUser: customer
            ? {
                image: customer.AvatarUrl,
                title: customer.Name,
                subtitle: Ultis.getDiffrentTime(post.DateCreated),
              }
            : null,
        };
      });
      setData(data);
      setLoading(false);
      if (props.setRefresh) props.setRefresh(false);
    }
  };

  // get update likes in api
  const handleLike = async (item: any) => {
    if (cusId) {
      const likeController = new DataController('Likes');
      // add like in api
      if (item.IsLike === true) {
        const result = await likeController.getListSimple({
          query: `@CustomerId: {${cusId}} @NewsId:{${item.Id}}`,
        });
        if (result.data?.length > 0) {
          const unlike = await likeController.delete([result.data[0].Id]);
          if (unlike.code === 200) {
            setData(prevData =>
              prevData.map(a =>
                a.Id === item.Id
                  ? {
                      ...item,
                      IsLike: false,
                      Likes: item.Likes.filter(
                        (a: any) => a.CustomerId !== cusId,
                      ),
                    }
                  : a,
              ),
            );
          }
        }
      } else {
        const data = {
          Id: randomGID(),
          CustomerId: cusId,
          NewsId: item.Id,
          Type: 1,
          DateCreated: new Date().getTime(),
        };
        const result = await likeController.add([data]);
        if (result.code === 200) {
          setData(prevData =>
            prevData.map(a =>
              a.Id === item.Id
                ? {...item, IsLike: true, Likes: [...item.Likes, data]}
                : a,
            ),
          );
        }
      }
    } else {
      dialogCheckAcc(dialogRef);
    }
  };

  // const handleLoadMore = async () => {
  //   setPage({...page, page: page.page + 1});
  //   setLoadMore(true);
  //   console.log('Loadmore...');
  //   const result = await SocialDA.getNewFeed(page.page, page.size);
  //   if (result) {
  //     setData([...data, ...result.data]);
  //     setLoadMore(false);
  //
  //   }
  // };

  // const [liked, setLiked] = useState(false);
  // const [saved, setSaved] = useState(false);

  return (
    <View style={{width: '100%', height: props.horizontal ? 386 : undefined}}>
      <FBottomSheet ref={popupRef} />
      <FDialog ref={dialogRef} />

      <FlatList
        data={data}
        nestedScrollEnabled
        scrollEnabled={false}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          gap: 24,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        ListHeaderComponent={() => {
          if (!props.titleList) return null;
          return (
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'space-between',
                paddingHorizontal: 16,
                flexDirection: 'row',
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Absolute,
              }}>
              <Text
                style={{
                  ...TypoSkin.heading6,
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}>
                {props.titleList}
              </Text>
              {props.isSeeMore ? (
                <AppButton
                  title={'See more'}
                  containerStyle={{
                    justifyContent: 'flex-start',
                    alignSelf: 'baseline',
                  }}
                  backgroundColor={'transparent'}
                  textStyle={TypoSkin.buttonText3}
                  borderColor="transparent"
                  suffixIconSize={16}
                  suffixIcon={'outline/arrows/circle-arrow-right'}
                  onPress={() => {}}
                  textColor={ColorThemes.light.Info_Color_Main}
                />
              ) : null}
            </View>
          );
        }}
        renderItem={({item, index}) => {
          return (
            <DefaultNew
              flexDirection="row"
              onPressDetail={() => {
                navigation.navigate(RootScreen.NewsDetail, {
                  item: item,
                });
              }}
              mainContainerStyle={{
                flexDirection: 'row-reverse',
                alignItems: 'flex-start',
              }}
              containerStyle={{
                paddingHorizontal: 16,
              }}
              data={item}
              actionView={
                <View
                  style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      gap: 4,
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                    }}>
                    <AppButton
                      backgroundColor={'transparent'}
                      borderColor="transparent"
                      onPress={async () => {
                        await handleLike(item);
                      }}
                      containerStyle={{padding: 4}}
                      title={`${item.Likes?.length ?? 0}`}
                      textColor={
                        item.IsLike === true
                          ? ColorThemes.light.Primary_Color_Main
                          : ColorThemes.light.Neutral_Text_Color_Subtitle
                      }
                      textStyle={TypoSkin.buttonText6}
                      prefixIconSize={16}
                      prefixIcon={
                        item.IsLike === true
                          ? 'fill/user interface/heart'
                          : 'outline/user interface/heart'
                      }
                    />
                    <AppButton
                      backgroundColor={'transparent'}
                      borderColor="transparent"
                      onPress={async () => {
                        var token = await getDataToAsyncStorage(
                          StorageContanst.accessToken,
                        );
                        if (token) {
                          navigation.navigate(RootScreen.NewsDetail, {
                            item: item,
                          });
                        } else {
                          dialogCheckAcc(dialogRef);
                        }
                      }}
                      containerStyle={{padding: 4}}
                      title={`${item.Comment ?? 0}`}
                      textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
                      textStyle={TypoSkin.buttonText6}
                      prefixIconSize={16}
                      prefixIcon={'outline/user interface/b-comment'}
                    />
                    <AppButton
                      backgroundColor={'transparent'}
                      borderColor="transparent"
                      onPress={() => {
                        onShare({
                          content: `${ConfigAPI.urlWeb}/news?id=${item?.Id}`,
                        });
                      }}
                      containerStyle={{padding: 4}}
                      title={
                        <Winicon src="fill/arrows/social-sharing" size={16} />
                      }
                    />
                  </View>
                </View>
              }
            />
          );
        }}
        style={{width: '100%', height: '100%'}}
        keyExtractor={item => item.Id?.toString()}
        horizontal={props.horizontal}
        onEndReachedThreshold={0.5}
        initialNumToRender={size}
        // onEndReached={() => {
        //   setPage(page + 1);
        //   dispatch(newsFeedActions.getNewFeedLoadmore(page,size));
        // }}
        ListEmptyComponent={() => {
          if (loading) {
            return (
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                }}>
                <ActivityIndicator
                  color={ColorThemes.light.Primary_Color_Main}
                />
              </View>
            );
          }
          return <EmptyPage />;
        }}
      />
    </View>
  );
}

const BottomSheetComments = forwardRef(function BottomSheetComments(
  data: {
    item: any;
  },
  ref: any,
) {
  const dispatch: AppDispatch = useDispatch();
  const {item} = data;
  const methods = useForm<any>({
    shouldFocusError: false,
  });
  return (
    <Pressable
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 65,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          paddingVertical: 4,
        }}
        title={`Bình luận`}
        prefix={<View />}
        action={
          <TouchableOpacity
            onPress={() => hideBottomSheet(ref)}
            style={{padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.Neutral_Text_Color_Body}
            />
          </TouchableOpacity>
        }
      />
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust for iOS and Android
        keyboardVerticalOffset={Platform.OS === 'ios' ? 65 : 0} // Offset for iOS
      >
        <ScrollView nestedScrollEnabled style={{flex: 1, height: '100%'}}>
          <View style={{paddingHorizontal: 16}}>
            <DefaultNew
              mainContainerStyle={{
                alignItems: 'center',
              }}
              data={{
                Img: item.Img,
                Name: item.Title,
                Id: item.Id,
                relativeUser: item?.relativeUser,
                Content: undefined,
                Description: undefined,
              }}
            />
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                gap: 4,
                justifyContent: 'flex-start',
                alignItems: 'center',
                borderBottomColor:
                  ColorThemes.light.Neutral_Border_Color_Bolder,
                borderBottomWidth: 0.5,
              }}>
              <AppButton
                backgroundColor={'transparent'}
                borderColor="transparent"
                onPress={async () => {
                  var token = await getDataToAsyncStorage(
                    StorageContanst.accessToken,
                  );
                  if (token) {
                    await dispatch(
                      newsFeedActions.updateLike(item.Id, item.IsLike === true),
                    );
                  } else {
                    ///TODO: check chưa login thì confirm ra trang login
                  }
                }}
                containerStyle={{padding: 4}}
                title={`${item.Likes ?? 0}`}
                textColor={
                  item.IsLike
                    ? ColorThemes.light.Primary_Color_Main
                    : ColorThemes.light.Neutral_Text_Color_Subtitle
                }
                textStyle={TypoSkin.buttonText6}
                prefixIconSize={16}
                prefixIcon={
                  item.IsLike
                    ? 'fill/user interface/like'
                    : 'outline/user interface/like'
                }
              />
              <AppButton
                backgroundColor={'transparent'}
                borderColor="transparent"
                onPress={undefined}
                containerStyle={{padding: 4}}
                title={`${item.Comment ?? 0}`}
                textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
                textStyle={TypoSkin.buttonText6}
                prefixIconSize={16}
                prefixIcon={'outline/user interface/b-comment'}
              />
            </View>
          </View>
          <Pressable style={{flex: 1}}>
            {/* <CommentsListNews Id={item.Id} /> */}
          </Pressable>
        </ScrollView>
        <View
          style={{
            flexDirection: 'row',
            gap: 8,
            paddingHorizontal: 16,
            paddingVertical: 16,
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <TextFieldForm
            textFieldStyle={{padding: 16}}
            style={{flex: 1}}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            placeholder="Write a comment"
            name="Comment"
          />
          <AppButton
            title={'Send'}
            backgroundColor={ColorThemes.light.Primary_Color_Main}
            borderColor="transparent"
            containerStyle={{
              borderRadius: 8,
              paddingHorizontal: 12,
              height: 45,
            }}
            onPress={async () => {
              var token = await getDataToAsyncStorage(
                StorageContanst.accessToken,
              );
              if (token) {
                if (methods.getValues().Comment) {
                  // await dispatch(
                  //   newsFeedActions.addComment(
                  //     item.Id,
                  //     methods.getValues().Comment,
                  //   ),
                  // );
                  methods.reset();
                }
              } else {
                ///TODO: check chưa login thì confirm ra trang login
              }
            }}
            textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
          />
        </View>
      </KeyboardAvoidingView>
    </Pressable>
  );
});
