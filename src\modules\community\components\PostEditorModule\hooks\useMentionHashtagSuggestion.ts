import {useState, useCallback} from 'react';
import {Dimensions} from 'react-native';
import { DataController } from '../../../../../base/baseController';

const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

export interface SuggestionUser {
  id: number;
  name: string;
  username: string;
  avatar?: string;
}

export interface SuggestionHashtag {
  id: number;
  tag: string;
}

export interface SuggestionState {
  visible: boolean;
  type: '@' | '#' | null;
  data: SuggestionUser[] | SuggestionHashtag[];
  position: {x: number; y: number};
  triggerStartIndex: number;
}

export const useMentionHashtagSuggestion = () => {
  const [suggestionState, setSuggestionState] = useState<SuggestionState>({
    visible: false,
    type: null,
    data: [],
    position: {x: 0, y: 0},
    triggerStartIndex: -1,
  });

  // Mock data - sẽ được thay thế bằng API call sau
  // const mockUsers: SuggestionUser[] = [
  //   {id: 1, name: '<PERSON>', username: 'joh<PERSON><PERSON>'},
  //   {id: 2, name: '<PERSON>', username: 'jane<PERSON>'},
  //   {id: 3, name: '<PERSON>', username: 'mikejohnson'},
  //   {id: 4, name: 'Sarah Wilson', username: 'sarahwilson'},
  //   {id: 5, name: 'David Brown', username: 'davidbrown'},
  // ];

  // const mockHashtags: SuggestionHashtag[] = [
  //   {id: 1, tag: 'react'},
  //   {id: 2, tag: 'reactnative'},
  //   {id: 3, tag: 'javascript'},
  //   {id: 4, tag: 'typescript'},
  //   {id: 5, tag: 'mobile'},
  //   {id: 6, tag: 'development'},
  // ];
  const getDataCustomer = async(searchText: string) => {
      const customercontroller = new DataController('Customer');
      const result = await customercontroller.getListSimple({
        query: `(@Name:(%${searchText}%)) | (@Name:(*${searchText}*)) | (@Mobile:(*${searchText}*))`,
      });
      if (result && result.code === 200) {
        const mappedUsers: SuggestionUser[] = result.data.map((user: any) => ({
          id: user.Id,
          name: user.Name,
          username: user.Email,
          avatar: user.AvatarUrl,
        }));
        return mappedUsers;
      } else {
        return [];
      }  
    };
    const getDataHashtag = async(searchText: string) => {
      const postcontroller = new DataController('Posts');
      const result = await postcontroller.group({
        searchRaw: `@ListTag:(*${searchText}*)`,
        reducers: `APPLY exists(@ListTag) as _exist FILTER @_exist==1 APPLY strlen(@ListTag) as _lentag FILTER @_lentag>0 APPLY split(@ListTag) AS tag GROUPBY 1 @tag REDUCE COUNT 0 AS count FILTER contains(lower(@tag),\"${searchText}\") SORTBY 2 @count DESC LIMIT 0 8`
      });
      if (result && result.code === 200) {
        const mappedHashtags: SuggestionHashtag[] = result.data.map((post: any) => ({
          id: post.Id,
          tag: post.tag,
        }));
        return mappedHashtags;
      } else {
        return [];
      }  
    };
  // Tính toán vị trí con trỏ
  const calculateCursorPosition = useCallback(
    (textBeforeCursor: string, fontSize = 16, lineHeight = 24) => {
      const lines = textBeforeCursor.split('\n');
      const currentLineIndex = lines.length - 1;
      const currentLineText = lines[currentLineIndex] || '';

      // Tính toán X (horizontal position)
      const avgCharWidth = fontSize * 0.6;
      const x = currentLineText.length * avgCharWidth;

      // Tính toán Y (vertical position)
      const y = currentLineIndex * lineHeight;

      return {x: Math.min(x, SCREEN_WIDTH - 200), y};
    },
    [],
  );

  // Xử lý khi text thay đổi
  const handleTextChange = useCallback(
    async (
      text: string,
      cursorPosition: number,
      textInputLayout: {x: number; y: number; width: number; height: number},
    ) => {
      // Tìm trigger (@ hoặc #) gần nhất trước cursor
      let triggerIndex = -1;
      let trigger: '@' | '#' | null = null;

      for (let i = cursorPosition - 1; i >= 0; i--) {
        if (text[i] === '@' || text[i] === '#') {
          // Kiểm tra xem trigger có ở đầu text hoặc sau space/newline không
          if (i === 0 || text[i - 1] === ' ' || text[i - 1] === '\n') {
            triggerIndex = i;
            trigger = text[i] as '@' | '#';
            break;
          }
        } else if (text[i] === ' ' || text[i] === '\n') {
          // Gặp space hoặc newline thì dừng tìm kiếm
          break;
        }
      }

      if (triggerIndex !== -1 && trigger) {
        // Lấy keyword sau trigger
        const keyword = text
          .substring(triggerIndex + 1, cursorPosition)
          .toLowerCase();

        // Filter suggestions dựa trên keyword
        let filteredSuggestions: SuggestionUser[] | SuggestionHashtag[] = [];
        if (trigger === '@') {
           filteredSuggestions = await getDataCustomer(keyword);
        } else if (trigger === '#') {
          filteredSuggestions = await getDataHashtag(keyword);
        }

        if (filteredSuggestions.length > 0) {
          // Tính toán vị trí hiển thị suggestion
          const textBeforeCursor = text.substring(0, cursorPosition);
          const cursorPos = calculateCursorPosition(textBeforeCursor);

          const baseTop = textInputLayout.y + cursorPos.y + 40;
          const baseLeft = textInputLayout.x + cursorPos.x;

          // Đảm bảo không bị tràn màn hình
          const adjustedLeft = Math.max(
            10,
            Math.min(baseLeft, SCREEN_WIDTH - 250),
          );
          const adjustedTop =
            baseTop > SCREEN_HEIGHT - 200 ? baseTop - 180 : baseTop;

          setSuggestionState({
            visible: true,
            type: trigger,
            data: filteredSuggestions,
            position: {x: adjustedLeft, y: adjustedTop},
            triggerStartIndex: triggerIndex,
          });
        } else {
          // Không có suggestion nào phù hợp
          setSuggestionState(prev => ({...prev, visible: false}));
        }
      } else {
        // Không tìm thấy trigger hợp lệ
        setSuggestionState(prev => ({...prev, visible: false}));
      }
    },
    [calculateCursorPosition],
  );

  // Xử lý khi chọn suggestion
  const handleSuggestionSelect = useCallback(
    (
      item: SuggestionUser | SuggestionHashtag,
      currentText: string,
      cursorPosition: number,
    ): {newText: string; newCursorPosition: number} => {
      const beforeTrigger = currentText.substring(
        0,
        suggestionState.triggerStartIndex,
      );
      const afterCursor = currentText.substring(cursorPosition);

      let insertText = '';
      if (suggestionState.type === '@') {
        insertText = `${(item as SuggestionUser).username} `;
      } else if (suggestionState.type === '#') {
        insertText = `${(item as SuggestionHashtag).tag} `;
      }

      const newText = beforeTrigger + insertText + afterCursor;
      const newCursorPosition = beforeTrigger.length + insertText.length;

      // Ẩn suggestion ngay lập tức
      setSuggestionState({
        visible: false,
        type: null,
        data: [],
        position: {x: 0, y: 0},
        triggerStartIndex: -1,
      });

      return {newText, newCursorPosition};
    },
    [suggestionState.triggerStartIndex, suggestionState.type],
  );

  // Ẩn suggestion
  const hideSuggestion = useCallback(() => {
    setSuggestionState(prev => ({...prev, visible: false}));
  }, []);

  return {
    suggestionState,
    handleTextChange,
    handleSuggestionSelect,
    hideSuggestion,
  };
};
