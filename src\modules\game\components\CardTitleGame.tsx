import {useEffect, useState, useCallback} from 'react';
import {Text, View, StyleSheet, Image, TouchableOpacity} from 'react-native';
import Sound from 'react-native-sound';
import ConfigAPI from '../../../Config/ConfigAPI';

interface CardTitleGameProps {
  title: string;
  showIcon?: boolean;
  audioUrl?: string | null;
}

export const CardTitleGame = ({
  title,
  showIcon,
  audioUrl,
}: CardTitleGameProps) => {
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [isPausedAudio, setIsPausedAudio] = useState(false);
  const [audioPlayer, setAudioPlayer] = useState<Sound | null>(null);

  // Dọn dẹp audio
  const cleanupAudio = useCallback(() => {
    if (audioPlayer) {
      audioPlayer.stop();
      audioPlayer.release();
      setAudioPlayer(null);
    }
    setIsPlayingAudio(false);
    setIsPausedAudio(false);
  }, [audioPlayer]);

  // Thiết lập audio
  const setupAudio = useCallback(
    (url: string) => {
      console.log('===== SETTING UP AUDIO =====');
      console.log('Original Audio URL:', url);
      cleanupAudio();

      // Get valid audio URL using ConfigAPI
      const validUrl = ConfigAPI.getValidLink(url);
      console.log('Valid Audio URL:', validUrl);

      Sound.setCategory('Playback');

      const sound = new Sound(validUrl, '', error => {
        if (error) {
          console.error('Failed to load audio:', error);
          return;
        }
        console.log('Audio loaded successfully');
        setAudioPlayer(sound);
      });
    },
    [cleanupAudio],
  );

  // Dừng audio
  const stopAudio = useCallback(() => {
    if (audioPlayer && (isPlayingAudio || isPausedAudio)) {
      audioPlayer.stop(() => {
        setIsPlayingAudio(false);
        setIsPausedAudio(false);
      });
    }
  }, [audioPlayer, isPlayingAudio, isPausedAudio]);

  // Phát audio với pause/resume functionality
  const playAudio = useCallback(() => {
    if (!audioPlayer) {
      console.log('No audio player available');
      return;
    }

    if (isPlayingAudio) {
      // Pause audio if currently playing
      audioPlayer.pause(() => {
        console.log('Audio paused');
        setIsPlayingAudio(false);
        setIsPausedAudio(true);
      });
    } else if (isPausedAudio) {
      // Resume audio if paused
      setIsPlayingAudio(true);
      setIsPausedAudio(false);
      console.log('===== RESUME AUDIO =====');
      audioPlayer.play(success => {
        setIsPlayingAudio(false);
        setIsPausedAudio(false);
        if (!success) {
          console.error('Audio playback failed');
        }
      });
    } else {
      // Start playing audio from beginning
      setIsPlayingAudio(true);
      setIsPausedAudio(false);
      console.log('===== PLAY AUDIO =====');
      audioPlayer.play(success => {
        setIsPlayingAudio(false);
        setIsPausedAudio(false);
        if (!success) {
          console.error('Audio playback failed');
        }
      });
    }
  }, [audioPlayer, isPlayingAudio, isPausedAudio]);

  useEffect(() => {
    if (audioUrl) {
      setupAudio(audioUrl);
    }
    return cleanupAudio;
  }, [audioUrl]);

  return (
    <View style={styles.container}>
      {showIcon && audioUrl && (
        <TouchableOpacity style={styles.audioContainer} onPress={playAudio}>
          <Text style={styles.audioIcon}>
            {isPlayingAudio ? '⏸️' : isPausedAudio ? '▶️' : '🔊'}
          </Text>
        </TouchableOpacity>
      )}
      <View style={styles.instruction}>
        <Text style={styles.wordText}>{title || ''}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FCF8E8',
    padding: 12,
    borderRadius: 12,
  },
  instruction: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  wordText: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
  },
  audioContainer: {
    height: '100%',
    marginRight: 8,
  },
  audio: {
    width: 26,
    height: 26,
  },
  audioIcon: {
    fontSize: 16,
    color: '#333',
  },
});
