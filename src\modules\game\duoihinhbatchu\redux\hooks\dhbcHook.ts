import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../../../redux/store/store';
import {setData} from '../../../../../redux/reducers/gameReducer';
import {nextLevel, nextQuestion, startGame, setData as setDhbcData} from '../dhbcReducer';
import {markHintUsed} from '../dhbcReducer';
import {loadGameConfig, loadGameQuestions} from '../dhbcAsyncThunk';
import {dhbcDa} from '../../da/dhbcDA';

const dhbcDA = new dhbcDa();

export const useDhbcHook = () => {
  const dispatch = useDispatch<AppDispatch>();

  const action = {
    setData: (data: any) => {
      dispatch(setDhbcData(data));
    },
    startGame: () => {
      dispatch(startGame());
    },
    nextQuestion: () => {
      dispatch(nextQuestion());
    },
    nextLevel: () => {
      dispatch(nextLevel());
    },
    getGameConfig: (gameId: string) => {
      return dispatch(loadGameConfig({gameId}) as any);
    },
    loadQuestions: (
      gameId: string,
      milestoneId: number,
      competenceId: string,
    ) => {
      return dispatch(
        loadGameQuestions({gameId, stage: milestoneId, competenceId}),
      );
    },
    markHintUsed: (questionId: string) => {
      dispatch(markHintUsed(questionId));
    },
  };

  return action;
};
