import {createSlice} from '@reduxjs/toolkit';
import {Question} from '../../models/models';

const defaultConfig = {
  gameId: '',
  scorePerLife: 0,
  maxLives: 0,
  timeLimit: 0,
  bonusScore: 0,
  isActive: false,
  gemHint: 0,
  score: 0,
  Time: 300,
};

function getConfig(currentLevel: number, state: State) {
  switch (currentLevel) {
    case 1:
      return state.configLv1;
    case 2:
      return state.configLv2;
    case 3:
      return state.configLv3;
    default:
      return defaultConfig;
  }
}

interface GameConfig {
  gameId: string;
  scorePerLife: number;
  maxLives: number;
  timeLimit: number;
  bonusScore: number;
  isActive: boolean;
  gemHint: number;
  score: number;
  Time: number;
}

interface State {
  dataListQuestion: Question[];
  listQuestions: Question[];
  currentQuestion: Question | null;
  currentLevel: number;
  totalQuestion: number;
  questionDone: number;
  configLv1: GameConfig;
  configLv2: GameConfig;
  configLv3: GameConfig;
  config: GameConfig;
  maxLevel: number;
  loading: boolean;
  timeLimit: number;
}

const initialState: State = {
  loading: false,
  dataListQuestion: [],
  listQuestions: [],
  currentQuestion: null,
  currentLevel: 0,
  maxLevel: 3,
  totalQuestion: 0,
  questionDone: 0,
  configLv1: defaultConfig,
  configLv2: defaultConfig,
  configLv3: defaultConfig,
  config: defaultConfig,
  timeLimit: 0,
};

export const SakuTCReducer = createSlice({
  name: 'SakuTCReducer',
  initialState,
  reducers: {
    setData(state, action) {
      state[action.payload.stateName] = action.payload.value;
    },
    startGame: state => {
      state.currentLevel = 1;
      state.timeLimit = state.configLv1.Time;
      const questionLevel = state.dataListQuestion.filter(
        question => question.level === state.currentLevel,
      );
      state.listQuestions = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.totalQuestion = questionLevel.length;
      state.questionDone = 0;
    },
    nextQuestion: state => {
      state.currentQuestion = state.listQuestions[state.questionDone];
    },
    nextLevel: state => {
      state.currentLevel = state.currentLevel + 1;
      const config = getConfig(state.currentLevel, state);
      state.timeLimit = config.Time;
      const questionLevel = state.dataListQuestion.filter(
        question => question.level === state.currentLevel,
      );
      state.listQuestions = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.questionDone = 0;
      state.totalQuestion = questionLevel.length;
    },
    restartLevel: state => {
      state.currentQuestion = state.listQuestions[0];
      state.questionDone = 0;
    },
    resetGame: state => {
      state.currentLevel = 1;
      state.currentQuestion = null;
      state.questionDone = 0;
      state.totalQuestion = 0;
      state.listQuestions = [];
      state.timeLimit = 0;
    },
    applyGameConfig: (state, action: {payload: any}) => {
      state.configLv1 = action.payload.configLv1;
      state.configLv2 = action.payload.configLv2;
      state.configLv3 = action.payload.configLv3;
      state.config = action.payload.config;
    },
  },
});

export const {
  setData,
  startGame,
  nextQuestion,
  nextLevel,
  restartLevel,
  applyGameConfig,
  resetGame,
} = SakuTCReducer.actions;

export default SakuTCReducer.reducer;
