import { useState, useEffect, useCallback } from 'react';
import AudioManager, { AudioState } from '../utils/AudioManager';

export const useAudioManager = () => {
  const [audioState, setAudioState] = useState<AudioState>(() => 
    AudioManager.getInstance().getState()
  );

  useEffect(() => {
    const audioManager = AudioManager.getInstance();
    
    // Subscribe to audio state changes
    const unsubscribe = audioManager.subscribe(setAudioState);
    
    // Cleanup subscription on unmount
    return unsubscribe;
  }, []);

  const initializeAudio = useCallback(async (audioPath: string) => {
    const audioManager = AudioManager.getInstance();
    try {
      await audioManager.initializeAudio(audioPath);
      audioManager.setVolume(0.3); // Set default volume
    } catch (error) {
      console.error('Failed to initialize audio:', error);
    }
  }, []);

  const playAudio = useCallback(async (loop: boolean = false) => {
    const audioManager = AudioManager.getInstance();
    try {
      await audioManager.playAudio(loop);
    } catch (error) {
      console.error('Failed to play audio:', error);
    }
  }, []);

  const pauseAudio = useCallback(() => {
    AudioManager.getInstance().pauseAudio();
  }, []);

  const resumeAudio = useCallback(() => {
    AudioManager.getInstance().resumeAudio();
  }, []);

  const stopAudio = useCallback(() => {
    AudioManager.getInstance().stopAudio();
  }, []);

  const clearAudio = useCallback(() => {
    AudioManager.getInstance().clearAudio();
  }, []);

  const forceCleanup = useCallback(() => {
    AudioManager.getInstance().forceCleanup();
  }, []);

  return {
    audioState,
    initializeAudio,
    playAudio,
    pauseAudio,
    resumeAudio,
    stopAudio,
    clearAudio,
    forceCleanup,
  };
};
