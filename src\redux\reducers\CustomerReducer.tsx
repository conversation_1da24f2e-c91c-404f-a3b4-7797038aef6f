import { createSlice, PayloadAction, Dispatch } from '@reduxjs/toolkit';
import { useNavigation } from '@react-navigation/native';
import { BaseDA } from '../../base/BaseDA';
import ConfigAPI from '../../Config/ConfigAPI';
import { DataController } from '../../base/baseController';
import {
  clearDataToAsyncStorage,
  getDataToAsyncStorage,
  removeDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../utils/AsyncStorage';
import store, { RootState } from '../store/store';
import { navigateReset, RootScreen } from '../../router/router';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';
import { getFcmToken } from '../../features/notifications/fcm/fcm_helper';
import { Sakupi, SakupiType, StorageContanst } from '../../Config/Contanst';
import { randomGID } from '../../utils/Utils';

interface CustomerSimpleResponse {
  data?: any;
  onLoading?: boolean;
  type?: string;
}

const initState: CustomerSimpleResponse = {
  data: undefined,
  onLoading: false,
};

export const customerSlice = createSlice({
  name: 'customer',
  initialState: initState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETINFOR':
          state.data = action.payload.data;
          break;
        case 'UPDATE':
          state.data = action.payload.data;
          break;
        case 'LOGOUT':
          state.data = undefined;
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    onFetching: state => {
      state.onLoading = true;
    },
  },
});

const { handleActions, onFetching } = customerSlice.actions;

export default customerSlice.reducer;

export class CustomerActions {
  static login = async (body: {
    type: 'phone' | 'apple' | 'google' | 'microsoft' | 'account';
    token?: string;
    deviceToken?: string;
    ggClientId?: string;
    phone?: string;
    password?: string;
    email?: string;
  }) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/login', {
      headers: { module: 'Customer', pid: ConfigAPI.pid },
      body: body,
    });
    if (res.code === 200) {
      saveDataToAsyncStorage('accessToken', `${res.data.accessToken}`);
      saveDataToAsyncStorage('refreshToken', `${res.data.refreshToken}`);
      saveDataToAsyncStorage(
        'timeRefresh',
        `${(Date.now() / 1000 + 9 * 60).toString()}`,
      );
    }
    return res;
  };

  static getInfor = (isLogin?: boolean) => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    const res = await BaseDA.get(ConfigAPI.url + 'data/getInfo', {
      headers: { module: 'Customer', pid: ConfigAPI.pid },
    });

    if (res.code === 200) {
      const controller = new DataController('Customer');
      const rankController = new DataController('CustomerRank');
      await saveDataToAsyncStorage(StorageContanst.CustomerId, res.data.Id);
      const deviceToken = await getDataToAsyncStorage('fcmToken');
      debugger
      // EDIT DEVICE TOKEN
      if (!res.data.DeviceToken?.includes(deviceToken) && deviceToken) {
        res.data.DeviceToken ??= '';
        res.data.DeviceToken += `,${deviceToken}`;
        res.data.DeviceToken = res.data.DeviceToken.split(',').filter(
          (devTk: string, i: number, arr: Array<string>) =>
            devTk.length && arr.indexOf(devTk) === i,
        );
        if (res.data.DeviceToken.length > 3) {
          res.data.DeviceToken = res.data.DeviceToken.slice(
            res.data.DeviceToken.length - 3,
          );
        }
        res.data.DeviceToken = res.data.DeviceToken.join(',');
        await controller.edit([res.data]);
      }
      debugger
      if (isLogin) {
        //update rank
        //lấy bản ghi trong HistoryScore type = login ngày hôm nay
        const historyController = new DataController('HistoryScore');
        const date = new Date();
        const today = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
        const endday = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
        const result = await historyController.getListSimple({
          query: `@CustomerId: {${res.data.Id}} @Type: {${SakupiType.login}} @DateCreated: [${today} ${endday}]`,
        });
        if (result?.data?.length === 0) {
          await controller.edit([
            { Id: res?.data?.Id, Rank: (res?.data?.Rank ?? 0) + Sakupi.login },
          ]);
          const historyController = new DataController('HistoryScore');
          const data = {
            Id: randomGID(),
            CustomerId: res?.data.Id,
            Score: Sakupi.login,
            Name: res?.data?.Name,
            DateCreated: new Date().getTime(),
            Type: 2,
            SakupiType: SakupiType.login,
          };
          await historyController.add([data]);
        }
      }
      const ranks = await rankController.getAll();
      debugger
      if (ranks.code === 200) {
        if (ranks.data.length > 0) {
          // kiểm tra rank của res.data trong list ranks, nếu rank =0 hoặc nếu rank khoảng 0-30 thì return, nếu rank khoảng 30-60 thì return, nếu 60-100 thì return, 0,30,60,100 là các string lấy từ field Score
          // lấy file icon
          // const file = await BaseDA.getFilesInfor(
          //   ranks.data.map((e: any) => e.Icon),
          // );
          // debugger
          // if (file.code === 200) {
          //   ranks.data = ranks.data.map((e: any) => {
          //     debugger
          //     const _file = file.data.find((f: any) => f.Id === e.Icon);
          //     return {
          //       ...e,
          //       Icon: _file?.Url,
          //     };
          //   });
          // }
          //
          // Sắp xếp ranks theo thứ tự giảm dần để tìm rank cao nhất mà user đủ điều kiện
          const sortedRanks = ranks.data.sort((a: any, b: any) => {
            return parseFloat(b.Score) - parseFloat(a.Score);
          });

          // Tìm rank cao nhất mà user đủ điều kiện (Rank >= Score)
          const rank = sortedRanks.find((e: any) => {
            return res.data.Rank >= parseFloat(e.Score);
          });

          res.data.RankInfor = rank;
          res.data.RanksData = ranks.data.sort((a: any, b: any) => {
            return parseFloat(a.Score) - parseFloat(b.Score);
          });
        }
      }
      if(res.data?.Mobile){
        saveDataToAsyncStorage('Mobile', res.data?.Mobile);
      }
      debugger
      dispatch(
        handleActions({
          type: 'GETINFOR',
          data: res.data,
        }),
      );
    } else if (res.code === 400) {
      // Het token
      removeDataToAsyncStorage('timeRefresh');
      removeDataToAsyncStorage('accessToken');
      removeDataToAsyncStorage('refreshToken');
    }
  };

  static checkPassword = async (phone: string, password?: string) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/checkPassword', {
      headers: { module: 'Customer', pid: ConfigAPI.pid },
      body: { phone: phone, password: password },
    });
    return res;
  };

  static hashPassword = async (password?: string) => {
    const res = await BaseDA.get(
      ConfigAPI.url + `data/bcrypt?password=${password}`,
      {
        headers: { module: 'Customer', pid: ConfigAPI.pid },
      },
    );
    return res;
  };

  static logout = () => async (dispatch: Dispatch) => {
    const deviceToken = await getDataToAsyncStorage('fcmToken');
    // Get customer data from parameters instead of store
    const customerController = new DataController('Customer');
    const user = store.getState().customer.data;
    if (!user) {
      navigateReset(RootScreen.login);
      dispatch(
        handleActions({
          type: 'LOGOUT',
        }),
      );
      return;
    }

    if (deviceToken && user?.DeviceToken?.includes(deviceToken)) {
      customerController.edit([
        {
          ...user,
          DeviceToken: user?.DeviceToken
            ? user.DeviceToken?.split(',')
              .filter((e: any) => e.length && e !== deviceToken)
              .join(',')
            : undefined,
        },
      ]);
    }
    // const res = await customerController.edit([
    //   {...user, DeviceToken: undefined},
    // ]);
    // if (res.code == 200) {
    await removeDataToAsyncStorage('accessToken');
    await removeDataToAsyncStorage('refreshToken');
    await removeDataToAsyncStorage('timeRefresh');
    // await removeDataToAsyncStorage('Mobile');
    await removeDataToAsyncStorage(StorageContanst.CustomerId);
    // await clearDataToAsyncStorage();
    navigateReset(RootScreen.login);
    // NotificationActions.reset(store.dispatch);
    dispatch(
      handleActions({
        type: 'LOGOUT',
      }),
    );
    // }
  };

  static edit = (user: any) => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    const controller = new DataController('Customer');
    // const user = store.getState().customer.data;
    const res = await controller.edit([{ ...user, RanksData: undefined, RankInfor: undefined }]);
    debugger
    if (res.code === 200) {
      showSnackbar({
        message: 'Cập nhật thông tin tài khoản thành công',
        status: ComponentStatus.SUCCSESS,
      });
      if(user?.Mobile){
        saveDataToAsyncStorage('Mobile', user?.Mobile);
      }
      dispatch(
        handleActions({
          type: 'UPDATE',
          data: user,
        }),
      );
    }
    return res;
  };

  static delete = async (
    dispatch: Dispatch,
    userId: string,
    navigation: any,
  ) => {
    dispatch(onFetching());
    const controller = new DataController('Customer');
    const res = await controller.delete([userId]);
    if (res.code === 200) {
      clearDataToAsyncStorage();
      getFcmToken();
      showSnackbar({
        message:
          'Tài khoản đã bị xóa khỏi hệ thống, vui lòng đăng nhập lại để sử dụng',
        status: ComponentStatus.WARNING,
      });
      navigation.reset({
        index: 0,
        routes: [{ name: RootScreen.login }],
      });
    }
  };
  //tạo action cho việc cập nhật rank và lưu vào lịch sử
  static updateRank =
    (type: number, rank?: number, gameId?: string) => async (dispatch: Dispatch) => {
      const controller = new DataController('Customer');
      const customer = store.getState().customer;
      console.log(rank);
      const res = await controller.edit([
        { Id: customer?.data?.Id, Rank: (customer?.data?.Rank ?? 0) + rank },
      ]);
      if (res?.code === 200) {
        // lưu vào bảng HistoryGame
        const historyController = new DataController('HistoryScore');
        const data = {
          Id: randomGID(),
          CustomerId: customer?.data.Id,
          GameId: gameId,
          Score: rank ?? 0,
          Name: customer?.data?.Name,
          DateCreated: new Date().getTime(),
          Type: 2,
          SakupiType: type,
        };
        await historyController.add([data]);
        dispatch(
          handleActions({
            type: 'UPDATE',
            data: { ...customer.data, Rank: (customer?.data?.Rank ?? 0) + rank },
          }),
        );
      }
    };
}
