import React from 'react';
import {
  View,
  Text,
  Modal,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Image,
  StyleSheet,
} from 'react-native';
import {UserMention, MentionModalState} from './types';

interface UserMentionModalProps {
  mentionModal: MentionModalState;
  onSelectUser: (user: UserMention) => void;
  onClose: () => void;
  onSearchChange: (text: string) => void;
}

const UserMentionModal: React.FC<UserMentionModalProps> = ({
  mentionModal,
  onSelectUser,
  onClose,
  onSearchChange,
}) => {
  // Sử dụng dữ liệu từ mentionModal state thay vì gọi API riêng

  const handleSearchChange = (text: string) => {
    onSearchChange(text);
  };

  const renderUserItem = ({item}: {item: UserMention}) => (
    <TouchableOpacity
      style={styles.userItem}
      onPress={() => onSelectUser(item)}>
      <Image
        source={{
          uri: item.AvatarUrl,
        }}
        style={styles.avatar}
      />
      <Text style={styles.userName}>{item?.Name || item?.Email}</Text>
    </TouchableOpacity>
  );

  if (!mentionModal.visible) {
    return null;
  }

  return (
    <Modal
      visible={mentionModal.visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}>
        <View style={styles.container}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={e => e.stopPropagation()}
            style={[
              styles.modalContainer,
              {
                top: mentionModal.position.y,
                left: 20,
                right: 20,
              },
            ]}>
            <View style={styles.searchContainer}>
              <TextInput
                style={styles.searchInput}
                placeholder="Tìm kiếm người dùng..."
                value={mentionModal.searchText}
                onChangeText={handleSearchChange}
                autoFocus={true}
              />
            </View>

            {mentionModal.loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#007AFF" />
                <Text style={styles.loadingText}>Đang tìm kiếm...</Text>
              </View>
            ) : (
              <FlatList
                data={mentionModal.users}
                renderItem={renderUserItem}
                keyExtractor={item => item.Id}
                style={styles.userList}
                showsVerticalScrollIndicator={false}
                ListEmptyComponent={
                  <View style={styles.emptyContainer}>
                    <Text style={styles.emptyText}>
                      Không tìm thấy người dùng nào
                    </Text>
                  </View>
                }
              />
            )}
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  container: {
    flex: 1,
    justifyContent: 'flex-start',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    maxHeight: 300,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    position: 'absolute',
    zIndex: 1000,
  },
  searchContainer: {
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
  },
  userList: {
    maxHeight: 300,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userName: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  emptyContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
  },
});

export default UserMentionModal;
