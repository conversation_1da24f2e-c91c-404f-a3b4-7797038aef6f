import {DataController} from '../../../../base/baseController';
import {getRandomElements, shuffleArray} from '../../../../utils/arrayUtils';
import {GetQuestionsResponse} from '../../sakutimban/types/sakuTBTypes';
import {Question} from '../models/models';

const validateQuestions = (questions: any): Question[] => {
  return questions.map((question: any) => {
    return {
      id: question.Id,
      level: question.Level,
      title: question.Name,
      code: question.Code,
      listWords: question.Answers.map((answer: any) => ({
        id: answer.Id,
        sort: answer.Sort,
        text: answer.Name,
      })),
      audioUrl: question.Audio,
      sort: question.Sort,
      hint: question.Suggest,
    };
  });
};

const validateGameConfig = (config: any) => {
  return {
    gameId: config.GameId,
    scorePerLife: config.Score,
    maxLives: config.LifeCount,
    timeLimit: config.Time,
    bonusScore: config.Bonus,
    isActive: config.IsActive,
    scoreHint: config.ScoreHint,
    score: config.Score,
  };
};

export class MghhDa {
  private questionController: DataController;
  //GameAnswer
  private answerController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuestion');
    this.answerController = new DataController('GameAnswer');
  }

  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number = 1,
    competenceId: string,
    questionCount: number = 5,
  ): Promise<Question[]> {
    try {
      const response: GetQuestionsResponse =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}]`,
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        });
      if (response.code === 200) {
        const questionLv1 = shuffleArray(
          getRandomElements(
            response.data.filter((question: any) => question.Level === 1),
            questionCount,
          ),
        );
        const questionLv2 = shuffleArray(
          getRandomElements(
            response.data.filter((question: any) => question.Level === 2),
            questionCount,
          ),
        );
        const questionLv3 = shuffleArray(
          getRandomElements(
            response.data.filter((question: any) => question.Level === 3),
            questionCount,
          ),
        );
        let questions = [...questionLv1, ...questionLv2, ...questionLv3];

        const answerResponse = await this.answerController.getListSimple({
          query: `@GameQuestionId: {${questions
            .map((q: any) => q.Id)
            .join(' | ')}}`,
        });
        if (
          answerResponse &&
          answerResponse.data &&
          answerResponse.data.length > 0
        ) {
          questions.forEach((question: any, index: number) => {
            question.Answers = answerResponse.data.filter(
              (answer: any) => answer.GameQuestionId === question.Id,
            );
            question.Sort = index + 1;
          });
        }

        const dataValidate = validateQuestions(questions);
        return dataValidate;
      } else {
        throw new Error(
          `API Error: ${response.message} (Code: ${response.code})`,
        );
      }
    } catch (error) {
      console.error('[MghhDa] Error fetching questions:', error);
      throw Error('Failed to fetch questions from API');
    }
  }

  static async getGameConfig(gameId: string): Promise<any> {
    try {
      const controller = new DataController('GameConfig');
      const response = await controller.getListSimple({
        query: `@GameId: {${gameId}}`,
      });
      if (
        response.code !== 200 ||
        !response.data ||
        response.data.length === 0
      ) {
        throw new Error(
          'No game config found or API returned unsuccessful response',
        );
      }
      const configLv1 = response.data.find((item: any) => item.Sort === 1);
      const configLv2 = response.data.find((item: any) => item.Sort === 2);
      const configLv3 = response.data.find((item: any) => item.Sort === 3);

      return {
        configLv1: validateGameConfig(configLv1),
        configLv2: validateGameConfig(configLv2),
        configLv3: validateGameConfig(configLv3),
      };
    } catch (error) {
      console.error('[GameConfigDA] Error loading game config:', error);
      throw error;
    }
  }
}
