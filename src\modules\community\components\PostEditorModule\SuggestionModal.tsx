import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Platform,
} from 'react-native';
import {
  SuggestionState,
  SuggestionUser,
  SuggestionHashtag,
} from './hooks/useMentionHashtagSuggestion';

interface SuggestionModalProps {
  suggestionState: SuggestionState;
  onSelectSuggestion: (item: SuggestionUser | SuggestionHashtag) => void;
  onClose: () => void;
}

const SuggestionModal: React.FC<SuggestionModalProps> = ({
  suggestionState,
  onSelectSuggestion,
  onClose,
}) => {
  if (!suggestionState.visible) {
    return null;
  }
  //random color
  const randomColor = () => {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  };
  const renderSuggestionItem = ({
    item,
  }: {
    item: SuggestionUser | SuggestionHashtag;
  }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => onSelectSuggestion(item)}>
      {suggestionState.type === '@' ? (
        <View style={styles.userSuggestion}>
          <View style={[styles.avatar, {backgroundColor: randomColor()}]}>
            <Text style={styles.avatarText}>
              {(item as SuggestionUser).name?.charAt(0).toUpperCase()}
            </Text>
          </View>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{(item as SuggestionUser).name}</Text>
            <Text style={styles.userUsername}>
              {(item as SuggestionUser).username}
            </Text>
          </View>
        </View>
      ) : (
        <View style={styles.hashtagSuggestion}>
          <Text style={styles.hashtagText}>
            {(item as SuggestionHashtag).tag}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const getSuggestionsStyle = () => ({
    position: 'absolute' as const,
    top: suggestionState.position.y,
    left: suggestionState.position.x,
    width: 240,
    maxHeight: 150,
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.25,
        shadowRadius: 4,
      },
      android: {
        elevation: 5,
      },
    }),
    zIndex: 1000,
  });

  return (
    <View style={getSuggestionsStyle()}>
      <View style={styles.suggestionsHeader}>
        <Text style={styles.suggestionsHeaderText}>
          {suggestionState.type === '@' ? '👥 Mention' : '🏷️ Hashtag'}
        </Text>
      </View>
      <FlatList
        data={suggestionState.data}
        renderItem={renderSuggestionItem}
        keyExtractor={item => item.id?.toString()}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  suggestionsHeader: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  suggestionsHeaderText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
  },
  suggestionItem: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  userSuggestion: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  avatarText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  userUsername: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  hashtagSuggestion: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hashtagIcon: {
    fontSize: 16,
    fontWeight: '600',
    color: '#34C759',
    marginRight: 8,
  },
  hashtagText: {
    fontSize: 14,
    color: '#34C759',
    fontWeight: '500',
  },
});

export default SuggestionModal;
