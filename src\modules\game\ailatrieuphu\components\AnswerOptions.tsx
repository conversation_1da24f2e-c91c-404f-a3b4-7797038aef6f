import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions, ViewStyle } from 'react-native';
import * as Animatable from 'react-native-animatable';

interface AnswerOptionsProps {
  options: string[];
  onSelectAnswer: (index: number) => void;
  selectedAnswer: number | null;
  correctAnswer: number | null;
  isEnabled: boolean;
  eliminatedOptions: number[];
}

const { width } = Dimensions.get('window');

const AnswerOptions: React.FC<AnswerOptionsProps> = ({
  options,
  onSelectAnswer,
  selectedAnswer,
  correctAnswer,
  isEnabled,
  eliminatedOptions,
}) => {
  // <PERSON><PERSON>ng các chữ cái đại diện cho các đáp án
  const optionLetters = ['A', 'B', 'C', 'D'];

  const getOptionStyle = (index: number): ViewStyle[] => {
    const styles: ViewStyle[] = [baseStyles.optionButton];

    // Nếu đáp án đã bị loại bỏ (50:50)
    if (eliminatedOptions.includes(index)) {
      styles.push(baseStyles.eliminatedOption);
    }

    // Nếu đáp án được chọn
    if (selectedAnswer === index) {
      styles.push(baseStyles.selectedOption);
    }

    // Nếu đã hiển thị đáp án đúng
    if (correctAnswer !== null) {
      if (index === correctAnswer) {
        styles.push(baseStyles.correctOption);
      } else if (selectedAnswer === index) {
        styles.push(baseStyles.incorrectOption);
      }
    }

    return styles;
  };

  return (
    <View style={baseStyles.container}>
      {options.map((option, index) => (
        <Animatable.View
          key={index}
          animation="fadeIn"
          delay={index * 300}
          duration={500}
          style={{ width: '100%' }}
        >
          <TouchableOpacity
            style={getOptionStyle(index)}
            onPress={() => isEnabled && !eliminatedOptions.includes(index) && onSelectAnswer(index)}
            disabled={!isEnabled || eliminatedOptions.includes(index)}
          >
            <View style={baseStyles.optionLetterContainer}>
              <Text style={baseStyles.optionLetter}>{optionLetters[index]}</Text>
            </View>
            <Text style={baseStyles.optionText}>{option}</Text>
          </TouchableOpacity>
        </Animatable.View>
      ))}
    </View>
  );
};

const baseStyles = StyleSheet.create({
  container: {
    width: width,
    alignSelf: 'center',
    // marginBottom: 20,
  },
  optionButton: {
    backgroundColor: '#1E3A8A',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#4C6EF5',
    overflow: 'hidden',
  },
  optionLetterContainer: {
    backgroundColor: '#4C6EF5',
    padding: 12,
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  optionLetter: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
  optionText: {
    color: 'white',
    fontSize: 16,
    paddingHorizontal: 16,
    flex: 1,
  },
  selectedOption: {
    borderColor: '#FFD700',
    backgroundColor: '#2C4BA1',
  },
  correctOption: {
    borderColor: '#10B981',
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
  },
  incorrectOption: {
    borderColor: '#EF4444',
    backgroundColor: 'rgba(239, 68, 68, 0.2)',
  },
  eliminatedOption: {
    opacity: 0.3,
    borderColor: '#6B7280',
  },
});

export default AnswerOptions;
