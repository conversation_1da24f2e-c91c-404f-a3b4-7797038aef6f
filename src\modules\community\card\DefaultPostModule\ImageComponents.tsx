import React, {memo} from 'react';
import {View, Text} from 'react-native';
import FastImage from '@d11/react-native-fast-image';
import ClickableImage from '../../../exam/components/ClickableImage';
import {imageStyles} from './styles';

// Common image props để tránh lặp lại
const defaultImageProps = {
  priority: FastImage.priority.normal,
  cache: FastImage.cacheControl.immutable,
};

// Tách thành component riêng và sử dụng memo để tránh render lại
export const SingleImage = memo(({imageUrl}: {imageUrl: string}) => (
  <ClickableImage
    source={{
      uri: imageUrl,
      ...defaultImageProps,
    }}
    style={imageStyles.fullImage}
    resizeMode="cover"
  />
));

// Component cho 2 ảnh
export const TwoImages = memo(({imageUrls}: {imageUrls: string[]}) => (
  <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
    <FastImage
      key="img-0"
      source={{
        uri: imageUrls[0],
        ...defaultImageProps,
      }}
      style={imageStyles.halfImage}
      resizeMode={FastImage.resizeMode.cover}
    />
    <FastImage
      key="img-1"
      source={{
        uri: imageUrls[1],
        ...defaultImageProps,
      }}
      style={imageStyles.halfImage}
      resizeMode={FastImage.resizeMode.cover}
    />
  </View>
));

// Component cho 3 ảnh
export const ThreeImages = memo(({imageUrls}: {imageUrls: string[]}) => (
  <View>
    <FastImage
      source={{
        uri: imageUrls[0],
        ...defaultImageProps,
      }}
      style={imageStyles.topFullImage}
      resizeMode={FastImage.resizeMode.cover}
    />
    <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
      <FastImage
        key="img-1"
        source={{
          uri: imageUrls[1],
          ...defaultImageProps,
        }}
        style={imageStyles.halfSmallImage}
        resizeMode={FastImage.resizeMode.cover}
      />
      <FastImage
        key="img-2"
        source={{
          uri: imageUrls[2],
          ...defaultImageProps,
        }}
        style={imageStyles.halfSmallImage}
        resizeMode={FastImage.resizeMode.cover}
      />
    </View>
  </View>
));

// Component cho 4+ ảnh, sử dụng FastImage thay cho ImageBackground
export const MultipleImages = memo(
  ({imageUrls, imageCount}: {imageUrls: string[]; imageCount: number}) => {
    // Nếu có đúng 4 ảnh, hiển thị 4 ảnh bằng nhau
    if (imageCount === 4) {
      return (
        <View
          style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
          }}>
          <FastImage
            key="img-0"
            source={{
              uri: imageUrls[0],
              ...defaultImageProps,
            }}
            style={imageStyles.quadImage}
            resizeMode={FastImage.resizeMode.cover}
          />
          <FastImage
            key="img-1"
            source={{
              uri: imageUrls[1],
              ...defaultImageProps,
            }}
            style={imageStyles.quadImage}
            resizeMode={FastImage.resizeMode.cover}
          />
          <FastImage
            key="img-2"
            source={{
              uri: imageUrls[2],
              ...defaultImageProps,
            }}
            style={imageStyles.quadImageBottom}
            resizeMode={FastImage.resizeMode.cover}
          />
          <FastImage
            key="img-3"
            source={{
              uri: imageUrls[3],
              ...defaultImageProps,
            }}
            style={imageStyles.quadImageBottom}
            resizeMode={FastImage.resizeMode.cover}
          />
        </View>
      );
    }

    // Nếu có hơn 4 ảnh, hiển thị 4 ảnh với overlay ở ảnh cuối
    return (
      <View
        style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
        }}>
        <FastImage
          key="img-0"
          source={{
            uri: imageUrls[0],
            ...defaultImageProps,
          }}
          style={imageStyles.quadImage}
          resizeMode={FastImage.resizeMode.cover}
        />
        <FastImage
          key="img-1"
          source={{
            uri: imageUrls[1],
            ...defaultImageProps,
          }}
          style={imageStyles.quadImage}
          resizeMode={FastImage.resizeMode.cover}
        />
        <FastImage
          key="img-2"
          source={{
            uri: imageUrls[2],
            ...defaultImageProps,
          }}
          style={imageStyles.quadImageBottom}
          resizeMode={FastImage.resizeMode.cover}
        />
        <View style={{position: 'relative', width: '49.5%', height: 116}}>
          <FastImage
            key="img-3"
            source={{
              uri: imageUrls[3],
              ...defaultImageProps,
            }}
            style={imageStyles.quadImageBottom}
            resizeMode={FastImage.resizeMode.cover}
          />
          {imageCount > 4 && (
            <View style={imageStyles.overlay}>
              <Text style={imageStyles.overlayText}>+{imageCount - 4}</Text>
            </View>
          )}
        </View>
      </View>
    );
  },
);
